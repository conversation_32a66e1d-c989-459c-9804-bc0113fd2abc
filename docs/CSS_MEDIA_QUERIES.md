# CSS 媒体查询优先级指南

本文档详细说明了 CSS 媒体查询的优先级规则和最佳实践。

## 🎯 媒体查询优先级规则

### 1. 基本优先级原则

CSS 媒体查询的优先级遵循以下规则：

1. **层叠性（Cascade）**：后定义的规则优先级更高
2. **特异性（Specificity）**：更具体的选择器优先级更高
3. **重要性（Importance）**：`!important` 声明具有最高优先级

### 2. 媒体查询的执行顺序

```css
/* ❌ 错误的顺序 - 小屏幕规则会被大屏幕规则覆盖 */
@media (max-width: 1200px) { /* 大屏幕 */ }
@media (max-width: 768px) { /* 小屏幕 */ }
@media (max-width: 480px) { /* 更小屏幕 */ }

/* ✅ 正确的顺序 - 从大到小，确保正确覆盖 */
@media (max-width: 1200px) { /* 大屏幕 */ }
@media (max-width: 768px) { /* 小屏幕 */ }
@media (max-width: 480px) { /* 更小屏幕 */ }
```

## 📱 响应式设计策略

### 1. 移动端优先（Mobile First）

```scss
// 基础样式（移动端）
.component {
  padding: 12px;
  font-size: 14px;
}

// 平板端
@media (min-width: 768px) {
  .component {
    padding: 16px;
    font-size: 16px;
  }
}

// 桌面端
@media (min-width: 1024px) {
  .component {
    padding: 24px;
    font-size: 18px;
  }
}
```

### 2. 桌面端优先（Desktop First）

```scss
// 基础样式（桌面端）
.component {
  padding: 24px;
  font-size: 18px;
}

// 平板端
@media (max-width: 1023px) {
  .component {
    padding: 16px;
    font-size: 16px;
  }
}

// 移动端
@media (max-width: 767px) {
  .component {
    padding: 12px;
    font-size: 14px;
  }
}
```

## 🛠️ 最佳实践

### 1. 使用标准断点

```scss
// 推荐的断点系统
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px
);

// 使用 Sass 混合器
@mixin mobile {
  @media (max-width: 767.98px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023.98px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}
```

### 2. 组织媒体查询

```scss
// ✅ 推荐：将媒体查询放在组件内部
.article-list {
  // 基础样式
  padding: 24px;
  max-width: 1200px;
  
  // 平板样式
  @include tablet {
    padding: 16px;
    max-width: 800px;
  }
  
  // 移动端样式
  @include mobile {
    padding: 12px;
    max-width: 100%;
  }
}

// ❌ 不推荐：将媒体查询分散在文件末尾
.article-list {
  padding: 24px;
  max-width: 1200px;
}

@media (max-width: 767px) {
  .article-list {
    padding: 12px;
    max-width: 100%;
  }
}
```

### 3. 避免重复和冲突

```scss
// ❌ 问题：重复的媒体查询和冲突的样式
@media (max-width: 768px) {
  .component {
    padding: 12px;
    margin: 10px;
  }
}

@media (max-width: 768px) {
  .component {
    padding: 16px; // 冲突！
    color: red;
  }
}

// ✅ 解决方案：合并相同的媒体查询
@media (max-width: 768px) {
  .component {
    padding: 12px;
    margin: 10px;
    color: red;
  }
}
```

## 🔧 实际应用示例

### 1. ArticleList 组件优化

```scss
.article-list {
  // 默认样式（桌面端）
  padding: 40px 60px;
  max-width: 1200px;
  margin: 0 auto;
  
  // 大平板 (1024px - 1279px)
  @media (max-width: 1279.98px) {
    padding: 32px 48px;
    max-width: 1000px;
  }
  
  // 小平板 (768px - 1023px)
  @media (max-width: 1023.98px) {
    padding: 24px 32px;
    max-width: 800px;
  }
  
  // 大手机 (576px - 767px)
  @media (max-width: 767.98px) {
    padding: 16px 24px;
    max-width: 100%;
  }
  
  // 小手机 (575px 及以下)
  @media (max-width: 575.98px) {
    padding: 12px 16px;
    max-width: 100%;
  }
}

.article-list-right {
  // 默认样式
  justify-content: flex-end;
  margin-left: 60px;
  max-width: 409px;
  
  // 平板端调整
  @media (max-width: 1023.98px) {
    margin-left: 20px;
    max-width: 320px;
  }
  
  // 移动端调整
  @media (max-width: 767.98px) {
    justify-content: flex-start;
    margin-left: 0;
    max-width: 100%;
  }
}
```

### 2. 使用 CSS 容器查询（现代方案）

```scss
// 现代浏览器支持的容器查询
.article-container {
  container-type: inline-size;
}

.article-list {
  padding: 24px;
  
  @container (max-width: 600px) {
    padding: 12px;
    flex-direction: column;
  }
  
  @container (min-width: 800px) {
    padding: 32px;
    display: grid;
    grid-template-columns: 2fr 1fr;
  }
}
```

## 🚨 常见问题和解决方案

### 1. 媒体查询不生效

```scss
// ❌ 问题：特异性不够
.component {
  padding: 20px !important;
}

@media (max-width: 768px) {
  .component {
    padding: 10px; // 不会生效，因为上面有 !important
  }
}

// ✅ 解决方案：提高特异性或移除 !important
@media (max-width: 768px) {
  .component {
    padding: 10px !important; // 或者移除上面的 !important
  }
}
```

### 2. 断点重叠问题

```scss
// ❌ 问题：断点重叠
@media (max-width: 768px) { /* 规则 A */ }
@media (min-width: 768px) { /* 规则 B */ }
// 在 768px 时，两个规则都会生效

// ✅ 解决方案：使用 .98px 避免重叠
@media (max-width: 767.98px) { /* 规则 A */ }
@media (min-width: 768px) { /* 规则 B */ }
```

### 3. 性能优化

```scss
// ✅ 使用高效的媒体查询
@media (max-width: 768px) {
  .component-1,
  .component-2,
  .component-3 {
    display: none;
  }
}

// ❌ 避免过多的媒体查询
@media (max-width: 768px) {
  .component-1 { display: none; }
}
@media (max-width: 768px) {
  .component-2 { display: none; }
}
@media (max-width: 768px) {
  .component-3 { display: none; }
}
```

## 🔍 调试技巧

### 1. 使用浏览器开发者工具

```css
/* 添加调试样式 */
@media (max-width: 768px) {
  body::before {
    content: "Mobile: max-width 768px";
    position: fixed;
    top: 0;
    left: 0;
    background: red;
    color: white;
    padding: 5px;
    z-index: 9999;
  }
}
```

### 2. 使用 CSS 自定义属性

```scss
:root {
  --current-breakpoint: "desktop";
}

@media (max-width: 1023px) {
  :root {
    --current-breakpoint: "tablet";
  }
}

@media (max-width: 767px) {
  :root {
    --current-breakpoint: "mobile";
  }
}

// 在 JavaScript 中检查当前断点
const currentBreakpoint = getComputedStyle(document.documentElement)
  .getPropertyValue('--current-breakpoint');
```

## 📋 检查清单

在编写媒体查询时，请检查以下项目：

- [ ] 媒体查询按正确顺序排列（移动端优先或桌面端优先）
- [ ] 断点值没有重叠（使用 .98px）
- [ ] 选择器特异性足够高
- [ ] 没有不必要的 `!important` 声明
- [ ] 相同断点的规则已合并
- [ ] 测试了所有目标设备和屏幕尺寸
- [ ] 性能已优化（避免过多的媒体查询）

遵循这些最佳实践，可以确保您的响应式设计在所有设备上都能正确工作！
