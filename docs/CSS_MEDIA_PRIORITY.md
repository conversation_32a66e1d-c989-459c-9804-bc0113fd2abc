# CSS 媒体查询优先级完全指南

## 🔍 媒体查询优先级的核心原则

### 1. CSS 优先级基本规则

CSS 优先级遵循以下基本规则（从高到低）：

1. **`!important` 声明**：最高优先级
2. **内联样式**：直接在 HTML 元素上的 `style` 属性
3. **选择器特异性**：ID > 类/属性/伪类 > 元素/伪元素
4. **源码顺序**：后定义的规则覆盖先定义的规则

### 2. 媒体查询本身不增加优先级

媒体查询本身**不会**增加选择器的特异性，它只是一个条件包装器。这意味着：

```css
/* 特异性: 0,1,0 */
.element { color: black; }

/* 特异性仍然是: 0,1,0 */
@media (max-width: 768px) {
  .element { color: red; }
}
```

如果这两个规则都匹配，且媒体查询条件为真，那么 `.element` 将是红色的，**仅仅因为它在源码中定义得更晚**，而不是因为它在媒体查询中。

## 🚨 常见问题与解决方案

### 问题 1: 媒体查询样式不生效

```css
/* 在文件开头 */
.element {
  color: black;
  font-size: 16px;
}

/* 在文件末尾 */
@media (max-width: 768px) {
  .element {
    color: red; /* 生效，因为源码顺序靠后 */
    font-size: 14px; /* 生效，因为源码顺序靠后 */
  }
}
```

但如果顺序颠倒：

```css
/* 在文件开头 */
@media (max-width: 768px) {
  .element {
    color: red; /* 不生效，因为源码顺序靠前 */
    font-size: 14px; /* 不生效，因为源码顺序靠前 */
  }
}

/* 在文件末尾 */
.element {
  color: black;
  font-size: 16px;
}
```

### 解决方案 1: 调整源码顺序

```css
/* ✅ 推荐：先定义基础样式，再定义媒体查询 */
.element {
  color: black;
  font-size: 16px;
}

@media (max-width: 768px) {
  .element {
    color: red;
    font-size: 14px;
  }
}
```

### 解决方案 2: 提高选择器特异性

```css
/* 基础样式 */
.element {
  color: black;
  font-size: 16px;
}

/* 提高媒体查询中选择器的特异性 */
@media (max-width: 768px) {
  .container .element {
    color: red;
    font-size: 14px;
  }
}
```

### 解决方案 3: 使用 !important（谨慎使用）

```css
/* 基础样式 */
.element {
  color: black;
  font-size: 16px;
}

/* 使用 !important 确保媒体查询中的样式优先 */
@media (max-width: 768px) {
  .element {
    color: red !important;
    font-size: 14px !important;
  }
}
```

## 📱 媒体查询嵌套与优先级

当媒体查询嵌套或重叠时，所有匹配的媒体查询都会应用，最终样式由 CSS 优先级规则决定：

```css
/* 宽屏样式 */
@media (min-width: 1024px) {
  .element { font-size: 18px; }
}

/* 中等屏幕样式 */
@media (max-width: 1023px) {
  .element { font-size: 16px; }
}

/* 窄屏样式 */
@media (max-width: 767px) {
  .element { font-size: 14px; }
}
```

在 767px 宽的屏幕上，后两个媒体查询都会匹配，但 `.element` 的 `font-size` 将是 14px，因为它在源码中定义得更晚。

## 🎯 最佳实践

### 1. 使用移动优先或桌面优先的一致方法

#### 移动优先（推荐）

```css
/* 基础样式（移动端） */
.element {
  font-size: 14px;
}

/* 平板端 */
@media (min-width: 768px) {
  .element {
    font-size: 16px;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .element {
    font-size: 18px;
  }
}
```

#### 桌面优先

```css
/* 基础样式（桌面端） */
.element {
  font-size: 18px;
}

/* 平板端 */
@media (max-width: 1023px) {
  .element {
    font-size: 16px;
  }
}

/* 移动端 */
@media (max-width: 767px) {
  .element {
    font-size: 14px;
  }
}
```

### 2. 避免混合使用 min-width 和 max-width

```css
/* ❌ 避免：混合使用会导致优先级混乱 */
@media (min-width: 768px) {
  .element { color: blue; }
}

@media (max-width: 1023px) {
  .element { color: green; }
}

/* 在 768px-1023px 范围内，哪个颜色会生效？ */
```

### 3. 使用 CSS 变量简化响应式设计

```css
:root {
  --font-size-base: 14px;
}

@media (min-width: 768px) {
  :root {
    --font-size-base: 16px;
  }
}

.element {
  font-size: var(--font-size-base);
}
```

### 4. 使用 Sass 混合器管理媒体查询

```scss
@mixin mobile {
  @media (max-width: 767px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

.element {
  font-size: 18px; // 桌面默认
  
  @include tablet {
    font-size: 16px;
  }
  
  @include mobile {
    font-size: 14px;
  }
}
```

## 🔧 Vue 组件中的媒体查询优先级

在 Vue 单文件组件中，媒体查询优先级遵循相同的规则，但有一些特殊考虑：

### 1. scoped 样式中的媒体查询

```vue
<style scoped>
/* 基础样式 */
.element {
  color: black;
}

/* 媒体查询 */
@media (max-width: 768px) {
  .element {
    color: red;
  }
}
</style>
```

Vue 的 scoped 样式会为选择器添加一个唯一属性（如 `data-v-123456`），但这不会改变媒体查询的优先级规则。

### 2. 多个 style 块的优先级

```vue
<style>
/* 全局样式 */
.element {
  color: black;
}
</style>

<style scoped>
/* 组件作用域样式 */
.element {
  color: blue;
}

@media (max-width: 768px) {
  .element {
    color: red;
  }
}
</style>
```

在这个例子中，优先级顺序是：
1. 全局 `.element` (color: black)
2. scoped `.element` (color: blue) - 覆盖全局样式
3. 媒体查询中的 scoped `.element` (color: red) - 在媒体查询条件满足时覆盖前两者

## 📋 媒体查询优先级检查清单

在处理媒体查询优先级问题时，请检查以下几点：

1. **源码顺序**：确保媒体查询在基础样式之后定义
2. **选择器特异性**：检查选择器的特异性是否足够高
3. **重叠的媒体查询**：检查是否有多个媒体查询同时匹配
4. **CSS 变量**：考虑使用 CSS 变量简化响应式设计
5. **!important**：只在必要时谨慎使用
6. **一致性**：坚持使用移动优先或桌面优先的一种方法
7. **组织结构**：将相关的媒体查询放在一起，避免分散

## 🚀 高级技巧

### 1. 使用 :where() 和 :is() 控制特异性

```css
/* 特异性为 0,0,1 */
:where(.header, .footer) {
  color: blue;
}

/* 媒体查询中的样式特异性为 0,1,0 */
@media (max-width: 768px) {
  .element {
    color: red; /* 会覆盖上面的样式，因为特异性更高 */
  }
}
```

### 2. 使用容器查询（Container Queries）

```css
.container {
  container-type: inline-size;
}

/* 基于容器宽度而非视口宽度的响应式设计 */
@container (max-width: 400px) {
  .element {
    font-size: 14px;
  }
}
```

### 3. 使用层叠层（@layer）控制优先级

```css
/* 定义层叠层 */
@layer base, components, utilities;

/* 基础样式 */
@layer base {
  .element {
    color: black;
  }
}

/* 组件样式 */
@layer components {
  .element {
    color: blue; /* 覆盖 base 层 */
  }
  
  @media (max-width: 768px) {
    .element {
      color: red; /* 在媒体查询条件满足时覆盖上面的样式 */
    }
  }
}

/* 工具类 */
@layer utilities {
  .text-green {
    color: green !important; /* 覆盖所有前面的样式 */
  }
}
```

## 🔍 调试媒体查询优先级

### 1. 使用浏览器开发者工具

- 检查元素的计算样式
- 查看样式的应用顺序和优先级
- 临时禁用特定样式规则

### 2. 添加调试边框

```css
/* 添加彩色边框来可视化不同媒体查询的应用 */
.element {
  border: 2px solid black;
}

@media (max-width: 1023px) {
  .element {
    border: 2px solid blue;
  }
}

@media (max-width: 767px) {
  .element {
    border: 2px solid red;
  }
}
```

### 3. 使用 CSS 自定义属性跟踪应用的媒体查询

```css
:root {
  --active-breakpoint: "desktop";
}

@media (max-width: 1023px) {
  :root {
    --active-breakpoint: "tablet";
  }
}

@media (max-width: 767px) {
  :root {
    --active-breakpoint: "mobile";
  }
}

/* 在 JavaScript 中检查当前断点 */
const breakpoint = getComputedStyle(document.documentElement)
  .getPropertyValue('--active-breakpoint');
console.log('Current breakpoint:', breakpoint);
```

## 总结

媒体查询本身不会增加 CSS 选择器的优先级，它们只是条件性地应用样式。要确保媒体查询中的样式正确覆盖基础样式，可以：

1. 调整源码顺序（最简单）
2. 提高选择器特异性
3. 使用 !important（谨慎使用）
4. 采用一致的响应式设计方法（移动优先或桌面优先）
5. 使用现代 CSS 功能如 CSS 变量、容器查询和层叠层

通过理解和应用这些原则，您可以创建出在各种屏幕尺寸下都能正确显示的响应式设计。
