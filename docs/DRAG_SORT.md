# 拖拽排序功能使用指南

本文档详细介绍如何在 OfficialBlock 项目中实现和使用拖拽排序功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install vuedraggable@next
```

### 2. 基础使用

```vue
<template>
  <draggable 
    v-model="list" 
    item-key="id"
    @start="drag = true"
    @end="drag = false"
  >
    <template #item="{ element }">
      <div class="drag-item">
        <div class="drag-handle">
          <icon-drag-arrow />
        </div>
        <span>{{ element.name }}</span>
      </div>
    </template>
  </draggable>
</template>

<script setup>
import { ref } from 'vue'
import draggable from 'vuedraggable'

const drag = ref(false)
const list = ref([
  { id: 1, name: '项目 1' },
  { id: 2, name: '项目 2' },
  { id: 3, name: '项目 3' }
])
</script>
```

## 🎯 核心配置

### 拖拽选项配置

```javascript
const dragOptions = computed(() => ({
  animation: 200,        // 动画持续时间
  group: 'description',  // 分组名称
  disabled: false,       // 是否禁用拖拽
  ghostClass: 'ghost',   // 拖拽时的幽灵样式类
  chosenClass: 'chosen', // 选中时的样式类
  dragClass: 'drag'      // 拖拽时的样式类
}))
```

### 组件数据配置

```javascript
const componentData = {
  tag: 'div',                    // 容器标签
  type: 'transition-group',      // 使用过渡组
  name: !drag ? 'flip-list' : null  // 过渡动画名称
}
```

## 📱 实际应用示例

### 1. ArticleList 组件中的按钮排序

```vue
<template>
  <draggable 
    v-model="item.data.buttonList" 
    :component-data="{
      tag: 'div',
      type: 'transition-group',
      name: !drag ? 'flip-list' : null
    }"
    v-bind="dragOptions"
    @start="drag = true"
    @end="drag = false"
    item-key="id"
  >
    <template #item="{ element: button }">
      <div class="item-button draggable-item">
        <div class="drag-handle">
          <icon-drag-arrow class="drag-icon" />
        </div>
        <icon-delete @click="handleDeleteButton(item.data.buttonList, button.id)" />
        <a-button type="primary">{{ button.text }}</a-button>
        <div class="item-action">
          <a-input v-model="button.text" placeholder="按钮文本" />
          <a-input v-model="button.url" placeholder="按钮链接" />
        </div>
      </div>
    </template>
  </draggable>
</template>
```

### 2. 链接列表排序

```vue
<template>
  <draggable 
    v-model="item.data.linkList" 
    v-bind="dragOptions"
    item-key="id"
  >
    <template #item="{ element: link }">
      <div class="item-button draggable-item">
        <div class="drag-handle">
          <icon-drag-arrow class="drag-icon" />
        </div>
        <a-link :href="link.url">{{ link.text }}</a-link>
        <div class="item-action">
          <a-input v-model="link.text" placeholder="链接文本" />
          <a-input v-model="link.url" placeholder="链接地址" />
        </div>
      </div>
    </template>
  </draggable>
</template>
```

### 3. 图片列表排序

```vue
<template>
  <draggable 
    v-model="item.data.imageList" 
    v-bind="dragOptions"
    item-key="id"
  >
    <template #item="{ element: img }">
      <div class="img-list-item draggable-item">
        <div class="drag-handle img-drag-handle">
          <icon-drag-arrow class="drag-icon" />
        </div>
        <div class="item-img-box" v-if="img.src">
          <img class="item-img" :src="img.src" />
        </div>
        <div class="item-right">
          <a-input v-model="img.src" placeholder="图片路径" />
          <a-upload :show-file-list="false" action="/" />
        </div>
      </div>
    </template>
  </draggable>
</template>
```

## 🎨 样式设计

### 基础拖拽样式

```scss
.draggable-item {
  position: relative;
  cursor: move;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.drag-handle {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  cursor: grab;
  padding: 4px;
  border-radius: 4px;
  
  &:hover {
    background-color: #f0f2f5;
  }
  
  &:active {
    cursor: grabbing;
  }
  
  .drag-icon {
    font-size: 16px;
    color: #86909c;
  }
}
```

### 拖拽动画样式

```scss
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}
```

### 图片拖拽特殊样式

```scss
.img-list-item {
  &.draggable-item {
    padding: 8px;
    border-radius: 4px;
    
    &:hover {
      background-color: #f8f9fa;
    }
  }
  
  .img-drag-handle {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
  }
}
```

## 🚫 拖拽限制功能

### 1. 分组隔离

为了防止不同类型的列表项互相拖拽，我们为每种类型设置了独立的分组：

```javascript
// 不同类型的拖拽配置选项
const buttonDragOptions = computed(() => ({
  animation: 200,
  group: 'buttons', // 按钮专用分组
  disabled: false,
  ghostClass: 'ghost'
}))

const linkDragOptions = computed(() => ({
  animation: 200,
  group: 'links', // 链接专用分组
  disabled: false,
  ghostClass: 'ghost'
}))

const categoryDragOptions = computed(() => ({
  animation: 200,
  group: 'categories', // 分类专用分组
  disabled: false,
  ghostClass: 'ghost'
}))
```

### 2. 单项隐藏拖拽图标

当列表只有一项时，自动隐藏拖拽图标并禁用拖拽功能：

```javascript
// 检查是否应该显示拖拽图标
const shouldShowDragHandle = (list: any[]) => {
  return list && list.length > 1
}
```

```vue
<template>
  <draggable
    v-model="item.data.buttonList"
    v-bind="buttonDragOptions"
    :disabled="!shouldShowDragHandle(item.data.buttonList)"
    item-key="id"
  >
    <template #item="{ element: button }">
      <div
        class="item-button draggable-item"
        :class="{ 'sortable-disabled': !shouldShowDragHandle(item.data.buttonList) }"
        :key="button.id"
      >
        <!-- 条件渲染拖拽图标 -->
        <div
          v-if="shouldShowDragHandle(item.data.buttonList)"
          class="drag-handle"
        >
          <icon-drag-arrow class="drag-icon" />
        </div>
        <!-- 其他内容 -->
      </div>
    </template>
  </draggable>
</template>
```

### 3. 禁用状态样式

为禁用状态添加特殊样式：

```scss
.draggable-item {
  cursor: move;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  // 当禁用拖拽时，不显示拖拽效果
  &.sortable-disabled {
    cursor: default;

    &:hover {
      box-shadow: none;
      transform: none;
    }
  }
}
```

## 🔧 高级功能

### 1. 分组拖拽

```vue
<template>
  <!-- 待办事项 -->
  <draggable 
    v-model="todoList" 
    :group="{ name: 'tasks', pull: true, put: true }"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="task-item todo">{{ element.text }}</div>
    </template>
  </draggable>
  
  <!-- 进行中 -->
  <draggable 
    v-model="doingList" 
    :group="{ name: 'tasks', pull: true, put: true }"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="task-item doing">{{ element.text }}</div>
    </template>
  </draggable>
  
  <!-- 已完成 -->
  <draggable 
    v-model="doneList" 
    :group="{ name: 'tasks', pull: true, put: true }"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="task-item done">{{ element.text }}</div>
    </template>
  </draggable>
</template>
```

### 2. 条件拖拽

```vue
<template>
  <draggable 
    v-model="list" 
    :disabled="!canDrag"
    :move="checkMove"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="drag-item" :class="{ disabled: !element.canMove }">
        {{ element.name }}
      </div>
    </template>
  </draggable>
</template>

<script setup>
const canDrag = ref(true)

const checkMove = (evt) => {
  // 检查是否允许移动
  return evt.draggedContext.element.canMove !== false
}
</script>
```

### 3. 拖拽事件处理

```vue
<template>
  <draggable 
    v-model="list" 
    @start="onStart"
    @end="onEnd"
    @add="onAdd"
    @remove="onRemove"
    @update="onUpdate"
    @sort="onSort"
    item-key="id"
  >
    <template #item="{ element }">
      <div class="drag-item">{{ element.name }}</div>
    </template>
  </draggable>
</template>

<script setup>
const onStart = (evt) => {
  console.log('开始拖拽', evt)
}

const onEnd = (evt) => {
  console.log('结束拖拽', evt)
}

const onAdd = (evt) => {
  console.log('添加元素', evt)
}

const onRemove = (evt) => {
  console.log('移除元素', evt)
}

const onUpdate = (evt) => {
  console.log('更新顺序', evt)
}

const onSort = (evt) => {
  console.log('排序变化', evt)
}
</script>
```

## 📋 最佳实践

### 1. 性能优化

```vue
<script setup>
// 使用 computed 缓存拖拽选项
const dragOptions = computed(() => ({
  animation: 200,
  group: 'description',
  disabled: isLoading.value,
  ghostClass: 'ghost'
}))

// 防抖处理拖拽结束事件
import { debounce } from 'lodash-es'

const handleDragEnd = debounce(() => {
  // 保存排序结果到服务器
  saveOrder()
}, 300)
</script>
```

### 2. 无障碍访问

```vue
<template>
  <draggable 
    v-model="list" 
    item-key="id"
    role="list"
    aria-label="可拖拽排序的列表"
  >
    <template #item="{ element }">
      <div 
        class="drag-item"
        role="listitem"
        :aria-label="`项目 ${element.name}，可拖拽排序`"
        tabindex="0"
        @keydown="handleKeydown"
      >
        <div class="drag-handle" aria-label="拖拽手柄">
          <icon-drag-arrow />
        </div>
        {{ element.name }}
      </div>
    </template>
  </draggable>
</template>
```

### 3. 错误处理

```vue
<script setup>
const handleDragError = (error) => {
  console.error('拖拽操作失败:', error)
  // 恢复原始顺序
  list.value = [...originalList.value]
  // 显示错误提示
  Message.error('排序失败，请重试')
}

const onEnd = async (evt) => {
  try {
    // 保存新的排序
    await saveOrder(list.value)
  } catch (error) {
    handleDragError(error)
  }
}
</script>
```

## 🔍 调试技巧

### 1. 调试拖拽事件

```javascript
const debugDrag = (evt) => {
  console.log('拖拽事件:', {
    type: evt.type,
    oldIndex: evt.oldIndex,
    newIndex: evt.newIndex,
    item: evt.item,
    from: evt.from,
    to: evt.to
  })
}
```

### 2. 可视化调试

```scss
// 添加调试边框
.debug-drag {
  .drag-item {
    border: 1px solid red;
  }
  
  .ghost {
    border: 2px dashed blue;
  }
  
  .chosen {
    border: 2px solid green;
  }
}
```

## 🚨 常见问题

### 1. 拖拽不生效

- 检查是否正确导入 vuedraggable
- 确认 v-model 绑定的数组有 item-key 对应的字段
- 检查 CSS 样式是否影响了拖拽功能

### 2. 动画效果异常

- 确保使用了 transition-group
- 检查 CSS 动画类名是否正确
- 避免在拖拽过程中修改 DOM 结构

### 3. 性能问题

- 对于大列表，考虑使用虚拟滚动
- 避免在拖拽事件中执行复杂计算
- 使用防抖处理频繁的保存操作

## 📚 参考资源

- [vuedraggable 官方文档](https://github.com/SortableJS/vue.draggable.next)
- [SortableJS 文档](https://sortablejs.github.io/Sortable/)
- [Vue 3 Transition 文档](https://vuejs.org/guide/built-ins/transition-group.html)

通过遵循这些指南和最佳实践，您可以在项目中成功实现流畅、用户友好的拖拽排序功能！
