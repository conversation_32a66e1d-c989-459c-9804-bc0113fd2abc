# 主题系统使用指南

OfficialBlock 组件库集成了完整的主题系统，基于 UnoCSS 和 CSS 变量，提供了灵活的主题定制能力。

## 🎨 特性

- **完整的颜色系统**: 包含主色调、辅助色、功能色和中性色
- **响应式字体系统**: 支持多种字体大小和字体族
- **UnoCSS 集成**: 提供原子化 CSS 工具类
- **暗色模式支持**: 自动适配系统主题偏好
- **主题持久化**: 自动保存用户的主题设置
- **预设主题**: 提供多种预设主题供快速切换
- **自定义主题**: 支持完全自定义的主题配置

## 📦 安装

主题系统已经内置在组件库中，无需额外安装。

## 🚀 快速开始

### 基础使用

```typescript
import { useTheme } from '@/composables/useTheme'

const { 
  currentTheme, 
  isDarkMode, 
  setTheme, 
  toggleDarkMode 
} = useTheme()

// 切换到预设主题
setTheme('dark')

// 切换暗色模式
toggleDarkMode()
```

### 在组件中使用

```vue
<template>
  <div class="my-component">
    <h1 class="text-theme-primary text-2xl font-bold">
      标题文本
    </h1>
    <p class="text-default text-muted">
      正文内容
    </p>
    <button class="btn-primary">
      主要按钮
    </button>
  </div>
</template>

<script setup lang="ts">
import { useTheme } from '@/composables/useTheme'

const { currentTheme, getThemeColor } = useTheme()

// 获取主题色
const primaryColor = getThemeColor('primary')
</script>
```

## 🎯 主题配置

### 预设主题

系统提供了以下预设主题：

- **default**: 默认主题，现代蓝色风格
- **dark**: 暗色主题，适合暗色环境
- **compact**: 紧凑主题，较小的字体和间距
- **large**: 大号主题，较大的字体和间距

### 自定义主题

```typescript
import { useTheme } from '@/composables/useTheme'

const { updateTheme, setTheme } = useTheme()

// 更新单个属性
updateTheme({
  primaryColor: '#ff6b6b',
  fontSize: '16px'
})

// 设置完整的自定义主题
setTheme({
  primaryColor: '#ff6b6b',
  secondaryColor: '#4ecdc4',
  fontSize: '16px',
  fontFamily: 'Roboto, sans-serif',
  borderRadius: '8px',
  spacing: '20px'
})
```

## 🎨 颜色系统

### 主题颜色

- **Primary**: 主色调，用于主要操作和强调
- **Secondary**: 辅助色，用于次要操作
- **Success**: 成功色，用于成功状态
- **Warning**: 警告色，用于警告状态
- **Error**: 错误色，用于错误状态

### 颜色变体

每种颜色都提供了从 50 到 950 的色阶：

```css
/* 主色调变体 */
.bg-primary-50   /* 最浅 */
.bg-primary-100
.bg-primary-200
...
.bg-primary-500  /* 标准色 */
...
.bg-primary-900
.bg-primary-950  /* 最深 */
```

## 📝 字体系统

### 字体大小

```css
.text-xs     /* 12px */
.text-sm     /* 14px - 默认字号 */
.text-base   /* 16px */
.text-lg     /* 18px */
.text-xl     /* 20px */
.text-2xl    /* 24px */
.text-3xl    /* 30px */
.text-4xl    /* 36px */
```

### 字体族

```css
.font-sans   /* Inter, sans-serif */
.font-mono   /* JetBrains Mono, monospace */
```

## 🛠️ UnoCSS 工具类

### 按钮样式

```css
.btn          /* 基础按钮样式 */
.btn-primary  /* 主要按钮 */
.btn-secondary /* 次要按钮 */
.btn-outline  /* 轮廓按钮 */
```

### 卡片样式

```css
.card         /* 基础卡片 */
.card-hover   /* 带悬停效果的卡片 */
```

### 输入框样式

```css
.input        /* 输入框样式 */
```

### 布局工具类

```css
.flex-center    /* 居中对齐 */
.flex-between   /* 两端对齐 */
.container-responsive /* 响应式容器 */
```

### 文本样式

```css
.text-emphasis      /* 强调文本 */
.text-muted        /* 次要文本 */
.text-theme-primary /* 主题色文本 */
.text-default      /* 默认字号文本 */
```

## 🌙 暗色模式

### 自动检测

系统会自动检测用户的系统主题偏好，并应用相应的主题。

### 手动切换

```typescript
import { useTheme } from '@/composables/useTheme'

const { toggleDarkMode, isDarkMode } = useTheme()

// 切换暗色模式
toggleDarkMode()

// 检查当前模式
console.log(isDarkMode.value) // true/false
```

## 💾 主题持久化

主题设置会自动保存到 localStorage，页面刷新后会自动恢复。

```typescript
import { useTheme } from '@/composables/useTheme'

const { saveTheme, exportTheme, importTheme } = useTheme()

// 手动保存主题
saveTheme()

// 导出主题配置
const config = exportTheme()

// 导入主题配置
importTheme(config)
```

## 📱 响应式设计

主题系统完全支持响应式设计，提供了多个断点：

```css
/* 断点定义 */
xs: 475px
sm: 640px
md: 768px
lg: 1024px
xl: 1280px
2xl: 1536px
```

## 🔧 高级用法

### CSS 变量

所有主题值都通过 CSS 变量暴露，可以直接在样式中使用：

```css
.my-element {
  color: var(--theme-primary);
  font-size: var(--font-size-default);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}
```

### 主题监听

```typescript
import { watch } from 'vue'
import { useTheme } from '@/composables/useTheme'

const { currentTheme, isDarkMode } = useTheme()

// 监听主题变化
watch(currentTheme, (newTheme) => {
  console.log('主题已更改:', newTheme)
})

// 监听暗色模式变化
watch(isDarkMode, (isDark) => {
  console.log('暗色模式:', isDark)
})
```

## 🎯 最佳实践

1. **使用预设主题**: 优先使用预设主题，确保设计一致性
2. **合理使用颜色**: 遵循颜色语义，不要滥用主题色
3. **响应式优先**: 使用响应式工具类确保在所有设备上的良好体验
4. **性能考虑**: UnoCSS 会自动优化未使用的样式，但仍要避免过度使用
5. **可访问性**: 确保颜色对比度符合可访问性标准

## 🔍 调试工具

开发环境下可以访问 UnoCSS Inspector 来调试样式：

```
http://localhost:5167/__unocss/
```

## 📚 相关资源

- [UnoCSS 官方文档](https://unocss.dev/)
- [Arco Design 主题定制](https://arco.design/vue/docs/theme)
- [CSS 变量 MDN 文档](https://developer.mozilla.org/zh-CN/docs/Web/CSS/Using_CSS_custom_properties)
