# 主题系统使用示例

本文档提供了 OfficialBlock 主题系统的实际使用示例。

## 🚀 快速开始

### 1. 基础主题切换

```vue
<template>
  <div class="app">
    <header class="app-header">
      <h1>我的应用</h1>
      <div class="theme-controls">
        <button @click="toggleDarkMode">
          {{ isDarkMode ? '🌙' : '☀️' }} 
          {{ isDarkMode ? '暗色' : '亮色' }}
        </button>
        <select @change="handleThemeChange">
          <option value="default">默认主题</option>
          <option value="compact">紧凑主题</option>
          <option value="large">大号主题</option>
        </select>
      </div>
    </header>
    
    <main class="app-content">
      <div class="card">
        <h2 class="text-theme-primary">欢迎使用主题系统</h2>
        <p class="text-default text-muted">
          这是一个使用主题系统的示例应用
        </p>
        <button class="btn-primary">
          主要操作
        </button>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useTheme } from 'officialblock'

const { 
  isDarkMode, 
  toggleDarkMode, 
  setTheme, 
  saveTheme 
} = useTheme()

const handleThemeChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  setTheme(target.value as any)
  saveTheme()
}
</script>

<style scoped>
.app {
  min-height: 100vh;
  background: var(--theme-gray-50);
  transition: background-color var(--transition-normal);
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: white;
  border-bottom: 1px solid var(--theme-gray-200);
}

.theme-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.app-content {
  padding: var(--spacing-xl);
}

.card {
  background: white;
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  max-width: 600px;
  margin: 0 auto;
}
</style>
```

### 2. 自定义主题配置

```vue
<template>
  <div class="theme-customizer">
    <h3>主题定制器</h3>
    
    <div class="customizer-section">
      <label>主色调</label>
      <input 
        type="color" 
        v-model="customTheme.primaryColor"
        @change="applyCustomTheme"
      >
    </div>
    
    <div class="customizer-section">
      <label>字体大小</label>
      <select 
        v-model="customTheme.fontSize"
        @change="applyCustomTheme"
      >
        <option value="12px">小 (12px)</option>
        <option value="14px">默认 (14px)</option>
        <option value="16px">大 (16px)</option>
        <option value="18px">特大 (18px)</option>
      </select>
    </div>
    
    <div class="customizer-section">
      <label>圆角大小</label>
      <input 
        type="range" 
        min="0" 
        max="20" 
        v-model="borderRadiusValue"
        @input="updateBorderRadius"
      >
      <span>{{ customTheme.borderRadius }}</span>
    </div>
    
    <div class="customizer-actions">
      <button @click="exportTheme" class="btn-outline">
        导出配置
      </button>
      <button @click="resetTheme" class="btn-secondary">
        重置
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useTheme, type ThemeConfig } from 'officialblock'

const { 
  setTheme, 
  resetTheme, 
  exportTheme, 
  saveTheme 
} = useTheme()

const customTheme = reactive<ThemeConfig>({
  primaryColor: '#3b82f6',
  secondaryColor: '#a855f7',
  fontSize: '14px',
  fontFamily: 'Inter, sans-serif',
  borderRadius: '6px',
  spacing: '16px'
})

const borderRadiusValue = ref(6)

const applyCustomTheme = () => {
  setTheme(customTheme)
  saveTheme()
}

const updateBorderRadius = () => {
  customTheme.borderRadius = `${borderRadiusValue.value}px`
  applyCustomTheme()
}
</script>
```

### 3. 响应式主题适配

```vue
<template>
  <div class="responsive-layout">
    <div class="layout-grid">
      <div 
        v-for="item in items" 
        :key="item.id"
        class="grid-item"
        :class="getResponsiveClass()"
      >
        <h4>{{ item.title }}</h4>
        <p>{{ item.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ResponsiveUtils } from 'officialblock'

const items = ref([
  { id: 1, title: '项目 1', description: '这是第一个项目' },
  { id: 2, title: '项目 2', description: '这是第二个项目' },
  { id: 3, title: '项目 3', description: '这是第三个项目' }
])

const currentBreakpoint = ref(ResponsiveUtils.getCurrentBreakpoint())

const getResponsiveClass = () => {
  return `breakpoint-${currentBreakpoint.value}`
}

let cleanup: (() => void) | null = null

onMounted(() => {
  cleanup = ResponsiveUtils.onBreakpointChange((breakpoint) => {
    currentBreakpoint.value = breakpoint
  })
})

onUnmounted(() => {
  cleanup?.()
})
</script>

<style scoped>
.layout-grid {
  display: grid;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

/* 响应式网格 */
.layout-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.breakpoint-xs .layout-grid,
.breakpoint-sm .layout-grid {
  grid-template-columns: 1fr;
}

.breakpoint-md .layout-grid {
  grid-template-columns: repeat(2, 1fr);
}

.breakpoint-lg .layout-grid,
.breakpoint-xl .layout-grid,
.breakpoint-2xl .layout-grid {
  grid-template-columns: repeat(3, 1fr);
}

.grid-item {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
</style>
```

### 4. 颜色工具使用

```vue
<template>
  <div class="color-tools-demo">
    <h3>颜色工具演示</h3>
    
    <div class="color-section">
      <h4>颜色生成器</h4>
      <input 
        type="color" 
        v-model="baseColor"
        @change="generateColorScale"
      >
      
      <div class="color-scale">
        <div 
          v-for="(color, scale) in colorScale"
          :key="scale"
          class="color-swatch"
          :style="{ backgroundColor: color }"
        >
          <span :style="{ color: getTextColor(color) }">
            {{ scale }}
          </span>
        </div>
      </div>
    </div>
    
    <div class="color-section">
      <h4>颜色混合</h4>
      <div class="color-mixer">
        <input type="color" v-model="color1" @change="mixColors">
        <span>+</span>
        <input type="color" v-model="color2" @change="mixColors">
        <span>=</span>
        <div 
          class="mixed-color"
          :style="{ backgroundColor: mixedColor }"
        ></div>
      </div>
      
      <div class="mixer-controls">
        <label>混合比例</label>
        <input 
          type="range" 
          min="0" 
          max="1" 
          step="0.1"
          v-model="mixRatio"
          @input="mixColors"
        >
        <span>{{ Math.round(mixRatio * 100) }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ThemeUtils } from 'officialblock'

const baseColor = ref('#3b82f6')
const colorScale = reactive<Record<string, string>>({})

const color1 = ref('#3b82f6')
const color2 = ref('#a855f7')
const mixedColor = ref('#3b82f6')
const mixRatio = ref(0.5)

const generateColorScale = () => {
  const scale = ThemeUtils.generateColorScale(baseColor.value)
  Object.assign(colorScale, scale)
}

const getTextColor = (backgroundColor: string) => {
  return ThemeUtils.getBestTextColor(backgroundColor)
}

const mixColors = () => {
  mixedColor.value = ThemeUtils.mixColors(
    color1.value, 
    color2.value, 
    mixRatio.value
  )
}

// 初始化
generateColorScale()
mixColors()
</script>

<style scoped>
.color-tools-demo {
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.color-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.color-scale {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.color-swatch {
  height: 60px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: var(--font-size-sm);
}

.color-mixer {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.mixed-color {
  width: 60px;
  height: 40px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--theme-gray-300);
}

.mixer-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.mixer-controls input[type="range"] {
  flex: 1;
}
</style>
```

### 5. 动画工具使用

```vue
<template>
  <div class="animation-demo">
    <h3>动画工具演示</h3>
    
    <div class="animation-section">
      <h4>数值动画</h4>
      <div class="counter-demo">
        <div class="counter">{{ animatedValue }}</div>
        <div class="counter-controls">
          <button @click="animateToValue(0)">归零</button>
          <button @click="animateToValue(100)">100</button>
          <button @click="animateToValue(1000)">1000</button>
        </div>
      </div>
    </div>
    
    <div class="animation-section">
      <h4>滚动动画</h4>
      <div class="scroll-demo">
        <div class="scroll-content" ref="scrollContainer">
          <div v-for="i in 20" :key="i" class="scroll-item">
            项目 {{ i }}
          </div>
        </div>
        <div class="scroll-controls">
          <button @click="scrollToTop">滚动到顶部</button>
          <button @click="scrollToBottom">滚动到底部</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { AnimationUtils } from 'officialblock'

const animatedValue = ref(0)
const scrollContainer = ref<HTMLElement>()

const animateToValue = (target: number) => {
  AnimationUtils.animate(
    animatedValue.value,
    target,
    1000,
    (value) => {
      animatedValue.value = Math.round(value)
    },
    AnimationUtils.easing.easeOutCubic
  )
}

const scrollToTop = () => {
  if (scrollContainer.value) {
    AnimationUtils.scrollTo(scrollContainer.value, 0, 500)
  }
}

const scrollToBottom = () => {
  if (scrollContainer.value) {
    const maxScroll = scrollContainer.value.scrollHeight - scrollContainer.value.clientHeight
    AnimationUtils.scrollTo(scrollContainer.value, maxScroll, 500)
  }
}
</script>

<style scoped>
.animation-demo {
  padding: var(--spacing-lg);
  max-width: 600px;
  margin: 0 auto;
}

.animation-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.counter {
  font-size: 3rem;
  font-weight: bold;
  text-align: center;
  color: var(--theme-primary);
  margin-bottom: var(--spacing-lg);
}

.counter-controls,
.scroll-controls {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
}

.scroll-content {
  height: 200px;
  overflow-y: auto;
  border: 1px solid var(--theme-gray-300);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
}

.scroll-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--theme-gray-200);
}

.scroll-item:last-child {
  border-bottom: none;
}
</style>
```

## 📱 移动端适配示例

```vue
<template>
  <div class="mobile-app" :class="{ 'is-mobile': isMobile }">
    <header class="mobile-header">
      <button 
        v-if="isMobile" 
        @click="toggleSidebar"
        class="menu-toggle"
      >
        ☰
      </button>
      <h1>移动应用</h1>
    </header>
    
    <div class="mobile-layout">
      <aside 
        class="mobile-sidebar"
        :class="{ 'is-open': sidebarOpen }"
      >
        <nav class="mobile-nav">
          <a href="#" class="nav-item">首页</a>
          <a href="#" class="nav-item">设置</a>
          <a href="#" class="nav-item">关于</a>
        </nav>
      </aside>
      
      <main class="mobile-content">
        <div class="content-grid">
          <div 
            v-for="item in items"
            :key="item.id"
            class="content-card"
          >
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ResponsiveUtils } from 'officialblock'

const isMobile = ref(ResponsiveUtils.isMobile())
const sidebarOpen = ref(false)

const items = ref([
  { id: 1, title: '卡片 1', description: '移动端卡片示例' },
  { id: 2, title: '卡片 2', description: '响应式布局演示' },
  { id: 3, title: '卡片 3', description: '主题系统集成' }
])

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

let cleanup: (() => void) | null = null

onMounted(() => {
  cleanup = ResponsiveUtils.onBreakpointChange(() => {
    isMobile.value = ResponsiveUtils.isMobile()
    if (!isMobile.value) {
      sidebarOpen.value = false
    }
  })
})

onUnmounted(() => {
  cleanup?.()
})
</script>

<style scoped>
.mobile-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.mobile-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--theme-primary);
  color: white;
  gap: var(--spacing-md);
}

.menu-toggle {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

.mobile-layout {
  display: flex;
  flex: 1;
  position: relative;
}

.mobile-sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid var(--theme-gray-200);
  transition: transform var(--transition-normal);
}

.is-mobile .mobile-sidebar {
  position: fixed;
  top: 60px;
  left: 0;
  height: calc(100vh - 60px);
  z-index: 1000;
  transform: translateX(-100%);
  box-shadow: var(--shadow-lg);
}

.is-mobile .mobile-sidebar.is-open {
  transform: translateX(0);
}

.mobile-nav {
  padding: var(--spacing-lg);
}

.nav-item {
  display: block;
  padding: var(--spacing-md);
  color: var(--theme-gray-700);
  text-decoration: none;
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
  transition: background-color var(--transition-fast);
}

.nav-item:hover {
  background: var(--theme-gray-100);
}

.mobile-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.content-grid {
  display: grid;
  gap: var(--spacing-lg);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.is-mobile .content-grid {
  grid-template-columns: 1fr;
}

.content-card {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.content-card h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--theme-gray-900);
}

.content-card p {
  margin: 0;
  color: var(--theme-gray-600);
  line-height: var(--line-height-normal);
}
</style>
```

这些示例展示了如何在实际项目中使用 OfficialBlock 主题系统的各种功能，包括主题切换、自定义配置、响应式适配、颜色工具和动画效果等。
