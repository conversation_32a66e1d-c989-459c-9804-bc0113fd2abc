# Sass 使用指南

本文档介绍如何在 OfficialBlock 项目中使用 Sass 和 `@` 路径别名。

## 🎯 功能特性

- ✅ **Sass 支持**: 完整的 Sass/SCSS 语法支持
- ✅ **@ 路径别名**: 使用 `@` 符号引用 `src` 目录
- ✅ **自动导入**: 全局 Sass 变量自动导入
- ✅ **变量系统**: 完整的设计令牌变量
- ✅ **混合器库**: 常用样式混合器
- ✅ **响应式工具**: 响应式断点混合器

## 📦 安装配置

### 1. 安装 Sass

```bash
npm install -D sass
```

### 2. Vite 配置

项目已经配置好了 Sass 和路径别名支持：

```javascript
// vite.config.mjs
export default defineConfig({
  resolve: {
    alias: {
      '@': resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variables.scss" as *;`,
        includePaths: [resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')]
      },
      sass: {
        additionalData: `@use "@/styles/variables.scss" as *`,
        includePaths: [resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')]
      }
    }
  }
})
```

## 🎨 Sass 变量系统

### 颜色变量

```scss
// 主色调
$primary-color: #0032a0;
$primary-hover-color: #0048e8;
$secondary-color: #a855f7;

// 功能色
$success-color: #10b981;
$warning-color: #f59e0b;
$error-color: #ef4444;

// 文本颜色
$text-primary: #1a1c20;
$text-white: #ffffff;
$text-tertiary: rgba(26, 28, 32, 0.6);
```

### 字体变量

```scss
// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;

// 字体族
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

// 字体权重
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
```

### 间距和尺寸

```scss
// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// 圆角
$radius-sm: 4px;
$radius-md: 6px;
$radius-lg: 8px;
$radius-xl: 12px;
$radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
```

## 🛠️ Sass 混合器

### 布局混合器

```scss
// 居中对齐
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 两端对齐
@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
```

### 按钮混合器

```scss
// 基础按钮
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: 1px solid transparent;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all $transition-fast;
}

// 主要按钮
@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: white;

  &:hover:not(:disabled) {
    background-color: $primary-hover-color;
  }
}

// 轮廓按钮
@mixin button-outline {
  @include button-base;
  background-color: transparent;
  color: $primary-color;
  border-color: $primary-color;

  &:hover:not(:disabled) {
    background-color: $primary-color;
    color: white;
  }
}
```

### 响应式混合器

```scss
// 断点变量
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// 响应式混合器
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}
```

## 📁 @ 路径别名使用

### 在 Vue 组件中使用

```vue
<template>
  <div class="my-component">
    <Button />
  </div>
</template>

<script setup lang="ts">
// 导入组件
import Button from '@/components/Button/index.vue'

// 导入工具函数
import { useTheme } from '@/composables/useTheme'

// 导入类型
import type { ThemeConfig } from '@/types/theme'
</script>

<style lang="scss" scoped>
// 导入 Sass 变量
@import '@/styles/variables.scss';

// 导入其他样式文件
@import '@/styles/mixins.scss';

.my-component {
  // 使用 Sass 变量
  color: $primary-color;
  padding: $spacing-lg;
  
  // 使用混合器
  @include flex-center;
  
  // 响应式样式
  @include mobile {
    padding: $spacing-md;
  }
}
</style>
```

### 在 Sass 文件中使用

```scss
// src/styles/components.scss
@import '@/styles/variables.scss';
@import '@/styles/mixins.scss';

.card {
  @include card;
  
  &__header {
    color: $text-primary;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }
  
  &__content {
    color: $text-tertiary;
    line-height: $line-height-normal;
  }
}
```

### 在 TypeScript 中使用

```typescript
// src/utils/theme.ts
import type { ThemeConfig } from '@/types/theme'
import { useTheme } from '@/composables/useTheme'

export const createTheme = (config: ThemeConfig) => {
  // 主题创建逻辑
}
```

## 🎯 实际使用示例

### 1. 创建按钮组件

```vue
<template>
  <button :class="buttonClass" @click="handleClick">
    <slot />
  </button>
</template>

<script setup lang="ts">
interface Props {
  variant?: 'primary' | 'secondary' | 'outline'
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'medium'
})

const buttonClass = computed(() => [
  'btn',
  `btn--${props.variant}`,
  `btn--${props.size}`
])
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.btn {
  @include button-base;
  
  &--primary {
    @include button-primary;
  }
  
  &--secondary {
    @include button-secondary;
  }
  
  &--outline {
    @include button-outline;
  }
  
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
  }
  
  &--large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-base;
  }
}
</style>
```

### 2. 创建卡片组件

```vue
<template>
  <div class="card">
    <header v-if="$slots.header" class="card__header">
      <slot name="header" />
    </header>
    <div class="card__content">
      <slot />
    </div>
    <footer v-if="$slots.footer" class="card__footer">
      <slot name="footer" />
    </footer>
  </div>
</template>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.card {
  @include card;
  
  &__header {
    padding-bottom: $spacing-md;
    border-bottom: 1px solid $gray-200;
    margin-bottom: $spacing-md;
    
    h1, h2, h3, h4, h5, h6 {
      margin: 0;
      color: $text-primary;
      font-weight: $font-weight-semibold;
    }
  }
  
  &__content {
    color: $text-tertiary;
    line-height: $line-height-normal;
  }
  
  &__footer {
    padding-top: $spacing-md;
    border-top: 1px solid $gray-200;
    margin-top: $spacing-md;
    @include flex-between;
  }
}
</style>
```

### 3. 响应式布局

```vue
<template>
  <div class="responsive-grid">
    <div v-for="item in items" :key="item.id" class="grid-item">
      {{ item.title }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.responsive-grid {
  display: grid;
  gap: $spacing-lg;
  padding: $spacing-lg;
  
  // 默认单列布局（移动端）
  grid-template-columns: 1fr;
  
  // 平板端：两列布局
  @include tablet {
    grid-template-columns: repeat(2, 1fr);
  }
  
  // 桌面端：三列布局
  @include desktop {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 大屏幕：四列布局
  @media (min-width: #{$breakpoint-xl}) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.grid-item {
  @include card;
  
  // 移动端样式
  @include mobile {
    padding: $spacing-md;
    font-size: $font-size-sm;
  }
  
  // 桌面端样式
  @include desktop {
    padding: $spacing-lg;
    font-size: $font-size-base;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: $shadow-lg;
    }
  }
}
</style>
```

## 🔧 最佳实践

### 1. 文件组织

```
src/
├── styles/
│   ├── variables.scss      # 全局变量
│   ├── mixins.scss        # 混合器
│   ├── utilities.scss     # 工具类
│   ├── components.scss    # 组件样式
│   └── theme.css         # CSS 变量（与主题系统集成）
├── components/
│   └── Button/
│       └── index.vue     # 导出文件
└── views/
    └── components/
        └── SassDemo.vue  # 演示页面
```

### 2. 命名规范

```scss
// 使用 BEM 命名规范
.component-name {
  // 块级样式
  
  &__element {
    // 元素样式
  }
  
  &--modifier {
    // 修饰符样式
  }
  
  &__element--modifier {
    // 元素修饰符样式
  }
}
```

### 3. 变量使用

```scss
// ✅ 推荐：使用语义化变量名
.button {
  background-color: $primary-color;
  padding: $spacing-md;
  border-radius: $radius-md;
}

// ❌ 不推荐：使用硬编码值
.button {
  background-color: #0032a0;
  padding: 16px;
  border-radius: 6px;
}
```

### 4. 混合器使用

```scss
// ✅ 推荐：使用混合器保持一致性
.card {
  @include card;
}

.button {
  @include button-primary;
}

// ❌ 不推荐：重复编写相同样式
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  padding: 24px;
}
```

## 🚀 演示页面

访问 `/components/sass` 查看完整的 Sass 使用演示，包括：

- Sass 变量展示
- 混合器使用示例
- 响应式设计演示
- @ 路径别名使用
- 实际组件示例

## 🔍 故障排除

### 常见问题

1. **@ 路径别名不工作**
   - 确保 `vite.config.mjs` 中配置了正确的别名
   - 检查 TypeScript 配置中的路径映射

2. **Sass 变量未定义**
   - 确保在 `vite.config.mjs` 中配置了 `additionalData`
   - 检查变量文件路径是否正确

3. **混合器不可用**
   - 确保正确导入了包含混合器的文件
   - 检查混合器语法是否正确

### 调试技巧

```scss
// 使用 @debug 输出变量值
@debug $primary-color;

// 使用 @warn 输出警告信息
@warn "This is a warning message";

// 使用条件语句检查变量
@if variable-exists(primary-color) {
  .element {
    color: $primary-color;
  }
} @else {
  @warn "Primary color variable not found";
}
```

现在您可以在项目中充分利用 Sass 的强大功能和 `@` 路径别名的便利性！
