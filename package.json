{"name": "officialblock", "private": false, "version": "1.0.5", "description": "A Vue 3 article list component library", "author": "Your Name", "license": "MIT", "keywords": ["vue", "vue3", "component", "article", "list"], "homepage": "https://github.com/yourusername/officialblock#readme", "repository": {"type": "git", "url": "git+https://github.com/yourusername/officialblock.git"}, "bugs": {"url": "https://github.com/yourusername/officialblock/issues"}, "main": "./dist/official-block.umd.js", "module": "./dist/official-block.es.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/official-block.es.js", "require": "./dist/official-block.umd.js"}, "./style.css": "./dist/style.css"}, "files": ["dist", "src", "types"], "scripts": {"dev": "vite", "build": "npx vite build --config vite.lib.config.mjs", "preview": "vite preview", "prepublishOnly": "npm run build"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@types/node": "^24.0.10", "@unocss/preset-attributify": "^66.3.3", "@unocss/preset-typography": "^66.3.3", "@unocss/preset-uno": "^66.3.3", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "typescript": "~5.8.3", "unocss": "^66.3.3", "vite": "^5.4.19", "vite-plugin-dts": "^4.5.4", "vue": "^3.5.17", "vue-tsc": "^2.2.12"}, "dependencies": {"@wangeditor/editor-for-vue": "^5.1.12", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}}