{"name": "officialblock", "private": false, "version": "1.0.0", "description": "A Vue 3 article list component library", "author": "Your Name", "license": "MIT", "keywords": ["vue", "vue3", "component", "article", "list"], "homepage": "https://github.com/yourusername/officialblock#readme", "repository": {"type": "git", "url": "git+https://github.com/yourusername/officialblock.git"}, "bugs": {"url": "https://github.com/yourusername/officialblock/issues"}, "main": "./dist/article-list.umd.js", "module": "./dist/article-list.es.js", "types": "./dist/types/index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/article-list.es.js", "require": "./dist/article-list.umd.js"}, "./style.css": "./dist/style.css"}, "files": ["dist", "src", "types"], "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "prepublishOnly": "npm run build"}, "peerDependencies": {"vue": "^3.0.0"}, "devDependencies": {"@types/node": "^24.0.10", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^5.4.19", "vite-plugin-dts": "^4.5.4", "vue": "^3.5.17", "vue-tsc": "^2.2.12"}}