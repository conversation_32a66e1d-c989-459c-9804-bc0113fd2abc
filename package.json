{"name": "officialblock", "private": true, "version": "0.0.0", "main": "./dist/officialblock.umd.js", "module": "./dist/officialblock.es.js", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/officialblock.es.js", "require": "./dist/officialblock.umd.js", "types": "./dist/types/index.d.ts"}, "./style.css": "./dist/style.css"}, "files": ["dist", "src", "types"], "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.0", "vite-plugin-dts": "^4.5.4", "vue-tsc": "^2.2.10"}}