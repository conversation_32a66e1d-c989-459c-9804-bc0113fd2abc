import { defineConfig, presetUno, presetAttributify, presetTypography } from 'unocss'

export default defineConfig({
  // 预设
  presets: [
    presetUno(), // 默认预设，包含Tailwind CSS兼容的工具类
    presetAttributify(), // 属性化模式
    presetTypography(), // 排版预设
  ],
  
  // 主题配置
  theme: {
    // 主题颜色配置
    colors: {
      // 主色调 - 现代蓝色系
      primary: {
        50: '#eff6ff',
        100: '#dbeafe', 
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6', // 主色
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        950: '#172554'
      },
      
      // 辅助色 - 现代紫色系
      secondary: {
        50: '#faf5ff',
        100: '#f3e8ff',
        200: '#e9d5ff', 
        300: '#d8b4fe',
        400: '#c084fc',
        500: '#a855f7', // 辅助色
        600: '#9333ea',
        700: '#7c3aed',
        800: '#6b21a8',
        900: '#581c87',
        950: '#3b0764'
      },
      
      // 成功色 - 现代绿色系
      success: {
        50: '#f0fdf4',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e', // 成功色
        600: '#16a34a',
        700: '#15803d',
        800: '#166534',
        900: '#14532d'
      },
      
      // 警告色 - 现代橙色系
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b', // 警告色
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f'
      },
      
      // 错误色 - 现代红色系
      error: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444', // 错误色
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d'
      },
      
      // 中性色 - 现代灰色系
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
        950: '#030712'
      }
    },
    
    // 字体大小配置
    fontSize: {
      'xs': ['0.75rem', { lineHeight: '1rem' }],      // 12px
      'sm': ['0.875rem', { lineHeight: '1.25rem' }],  // 14px - 默认字号
      'base': ['1rem', { lineHeight: '1.5rem' }],     // 16px
      'lg': ['1.125rem', { lineHeight: '1.75rem' }],  // 18px
      'xl': ['1.25rem', { lineHeight: '1.75rem' }],   // 20px
      '2xl': ['1.5rem', { lineHeight: '2rem' }],      // 24px
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],   // 36px
      '5xl': ['3rem', { lineHeight: '1' }],           // 48px
      '6xl': ['3.75rem', { lineHeight: '1' }],        // 60px
      '7xl': ['4.5rem', { lineHeight: '1' }],         // 72px
      '8xl': ['6rem', { lineHeight: '1' }],           // 96px
      '9xl': ['8rem', { lineHeight: '1' }],           // 128px
    },
    
    // 字体族配置
    fontFamily: {
      sans: [
        'Inter',
        '-apple-system',
        'BlinkMacSystemFont',
        'Segoe UI',
        'Roboto',
        'Helvetica Neue',
        'Arial',
        'Noto Sans',
        'sans-serif',
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji'
      ],
      mono: [
        'JetBrains Mono',
        'Fira Code',
        'Monaco',
        'Consolas',
        'Liberation Mono',
        'Courier New',
        'monospace'
      ]
    },
    
    // 断点配置
    breakpoints: {
      'xs': '475px',
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px'
    },
    
    // 阴影配置
    boxShadow: {
      'sm': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      'DEFAULT': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
      'md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      'lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      'xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
      '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
      'inner': 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
      'none': 'none'
    }
  },
  
  // 自定义规则
  rules: [
    // 自定义主题色快捷类
    ['text-theme-primary', { color: 'var(--theme-primary, #3b82f6)' }],
    ['bg-theme-primary', { 'background-color': 'var(--theme-primary, #3b82f6)' }],
    ['border-theme-primary', { 'border-color': 'var(--theme-primary, #3b82f6)' }],
    
    // 自定义字号快捷类
    ['text-default', { 'font-size': 'var(--font-size-default, 0.875rem)', 'line-height': '1.25rem' }],
    
    // 自定义间距
    ['p-safe', { padding: 'var(--safe-area-inset-top, 0) var(--safe-area-inset-right, 0) var(--safe-area-inset-bottom, 0) var(--safe-area-inset-left, 0)' }],
  ],
  
  // 快捷方式
  shortcuts: [
    // 按钮样式
    ['btn', 'px-4 py-2 rounded-lg font-medium transition-all duration-200 cursor-pointer'],
    ['btn-primary', 'btn bg-primary-500 text-white hover:bg-primary-600 active:bg-primary-700'],
    ['btn-secondary', 'btn bg-secondary-500 text-white hover:bg-secondary-600 active:bg-secondary-700'],
    ['btn-outline', 'btn border border-gray-300 text-gray-700 hover:bg-gray-50 active:bg-gray-100'],
    
    // 卡片样式
    ['card', 'bg-white rounded-lg shadow-md border border-gray-200'],
    ['card-hover', 'card hover:shadow-lg transition-shadow duration-200'],
    
    // 输入框样式
    ['input', 'px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent'],
    
    // 文本样式
    ['text-muted', 'text-gray-500'],
    ['text-emphasis', 'text-gray-900 font-medium'],
    
    // 布局样式
    ['flex-center', 'flex items-center justify-center'],
    ['flex-between', 'flex items-center justify-between'],
    
    // 响应式容器
    ['container-responsive', 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'],
  ],
  
  // 变体配置
  variants: [
    // 支持 hover:, focus:, active: 等状态
    (matcher) => {
      if (!matcher.startsWith('hover:') && !matcher.startsWith('focus:') && !matcher.startsWith('active:')) {
        return matcher
      }
    }
  ],
  
  // 提取器配置
  content: {
    pipeline: {
      include: [
        // 包含的文件类型
        /\.(vue|svelte|[jt]sx?|mdx?|astro|elm|php|phtml|html)($|\?)/,
        // 包含 src 目录下的所有文件
        'src/**/*.{js,ts,jsx,tsx,vue,html,css,scss,sass,less,styl,stylus}',
      ],
      exclude: [
        'node_modules',
        '.git',
        '.nuxt',
        '.output',
        'dist',
        '.vscode',
        '.idea'
      ]
    }
  }
})
