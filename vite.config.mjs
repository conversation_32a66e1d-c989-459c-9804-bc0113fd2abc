import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'
import dts from 'vite-plugin-dts'

export default defineConfig(async () => {
  const { default: UnoCSS } = await import('unocss/vite')

  return {
    plugins: [
      vue(),
      UnoCSS(),
      dts({
        entryRoot: 'src',
        outDir: 'dist/types',
        include: ['src/**/*.ts', 'src/**/*.vue'],
        exclude: ['src/**/*.test.*', 'src/**/*.spec.*'],
        staticImport: true,
        insertTypesEntry: true,
        copyDtsFiles: true
      })
    ],
  server: {
    port: 5166,
    host: true
  },
  build: {
    lib: {
      entry: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src/index.ts'),
      name: 'OfficialBlock',
      formats: ['es', 'umd', 'cjs'],
      fileName: (format) => `official-block.${format}.js`
    },
    rollupOptions: {
      external: ['vue'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
    },
    resolve: {
      alias: {
        '@': resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: (content, filename) => {
            // 避免在 variables.scss 文件中循环导入自己
            if (filename.includes('variables.scss')) {
              return content
            }
            return `@import "@/styles/variables.scss";\n${content}`
          },
          includePaths: [resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')]
        },
        sass: {
          additionalData: (content, filename) => {
            // 避免在 variables.scss 文件中循环导入自己
            if (filename.includes('variables.scss')) {
              return content
            }
            return `@import "@/styles/variables.scss"\n${content}`
          },
          includePaths: [resolve(fileURLToPath(new URL('.', import.meta.url)), 'src')]
        }
      }
    }
  }
})