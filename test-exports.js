// 测试导出是否正确
const fs = require('fs');
const path = require('path');

// 检查构建文件是否存在
const distPath = path.join(__dirname, 'dist');
const files = [
  'official-block.es.js',
  'official-block.umd.js',
  'official-block.cjs.js',
  'style.css',
  'types/index.d.ts'
];

console.log('检查构建文件:');
files.forEach(file => {
  const filePath = path.join(distPath, file);
  const exists = fs.existsSync(filePath);
  console.log(`${file}: ${exists ? '✓' : '✗'}`);
});

// 检查 ES 模块导出
try {
  const esContent = fs.readFileSync(path.join(distPath, 'official-block.es.js'), 'utf8');
  console.log('\nES 模块导出检查:');
  console.log('包含 ArticleList:', esContent.includes('ArticleList') ? '✓' : '✗');
  console.log('包含 HeroSlide:', esContent.includes('HeroSlide') ? '✓' : '✗');
  console.log('包含 default export:', esContent.includes('export{') && esContent.includes('default') ? '✓' : '✗');
} catch (e) {
  console.log('读取 ES 模块文件失败:', e.message);
}

// 检查类型文件
try {
  const typesContent = fs.readFileSync(path.join(distPath, 'types/index.d.ts'), 'utf8');
  console.log('\n类型文件检查:');
  console.log('包含 ComponentProps:', typesContent.includes('ComponentProps') ? '✓' : '✗');
  console.log('包含 HeroSlideProps:', typesContent.includes('HeroSlideProps') ? '✓' : '✗');
  console.log('包含 ArticleList 声明:', typesContent.includes('declare const ArticleList') ? '✓' : '✗');
  console.log('包含 HeroSlide 声明:', typesContent.includes('declare const HeroSlide') ? '✓' : '✗');
  console.log('包含默认导出:', typesContent.includes('export default OfficialBlock') ? '✓' : '✗');
} catch (e) {
  console.log('读取类型文件失败:', e.message);
}
