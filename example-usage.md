# OfficialBlock 使用示例

## 1. 全部引入方式

```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)
app.mount('#app')
```

```vue
<!-- App.vue -->
<template>
  <div>
    <!-- 直接使用，无需导入 -->
    <ArticleList v-model="value" />
    <HeroSlide />
  </div>
</template>

<script setup>
import { ref } from 'vue'
const value = ref('测试内容')
</script>
```

## 2. 按需引入方式

```vue
<template>
  <div>
    <ArticleList 
      v-model="articleValue"
      size="large"
      :disabled="false"
      @change="handleChange"
    >
      <template #header="{ title }">
        <h2>{{ title }}</h2>
      </template>
    </ArticleList>
    
    <HeroSlide 
      :auto-play-interval="4000"
      :show-indicators="true"
      @change="handleSlideChange"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const articleValue = ref('Hello OfficialBlock!')

const handleChange = (value) => {
  console.log('ArticleList 值改变:', value)
}

const handleSlideChange = (index) => {
  console.log('HeroSlide 切换到:', index)
}
</script>
```

## 3. TypeScript 使用

```vue
<template>
  <div>
    <ArticleList v-bind="articleProps" @change="handleChange" />
    <HeroSlide v-bind="heroProps" @change="handleSlideChange" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ArticleList, HeroSlide } from 'officialblock'
import type { ComponentProps, HeroSlideProps } from 'officialblock'
import 'officialblock/style.css'

const articleProps: ComponentProps = {
  modelValue: 'TypeScript 示例',
  size: 'medium',
  disabled: false
}

const heroProps: HeroSlideProps = {
  autoPlayInterval: 5000,
  showIndicators: true,
  autoPlay: true
}

const handleChange = (value: string | number) => {
  console.log('值改变:', value)
}

const handleSlideChange = (index: number) => {
  console.log('幻灯片索引:', index)
}
</script>
```

## 4. 插件方式引入

```javascript
// main.js
import { createApp } from 'vue'
import App from './App.vue'
import { ArticleListPlugin } from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
// 只注册需要的组件
app.use(ArticleListPlugin)
app.mount('#app')
```

## 常见问题解决

### 问题1: 组件没有渲染
- 确保已经导入样式文件 `import 'officialblock/style.css'`
- 检查组件名称是否正确（区分大小写）

### 问题2: TypeScript 类型错误
- 确保安装了最新版本的包
- 检查 tsconfig.json 中是否包含了 node_modules 类型

### 问题3: 按需引入失败
- 使用具名导入：`import { ArticleList, HeroSlide } from 'officialblock'`
- 不要使用默认导入进行按需引入
