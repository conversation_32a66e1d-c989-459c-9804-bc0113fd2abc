<template>
  <div class="app">
    <header class="header">
      <h1>OfficialBlock 组件库</h1>
      <p>本地开发预览</p>
    </header>

    <main class="main">
      <section class="component-section">
        <h2>HeroSlide 轮播组件</h2>
        <div class="component-demo">
          <HeroSlide />
        </div>
      </section>

      <section class="component-section">
        <h2>ArticleList 文章列表组件</h2>
        <div class="component-demo">
          <ArticleList
            v-model="articleValue"
            size="medium"
            :disabled="false"
            @change="(value) => console.log('ArticleList changed:', value)"
          >
            <template #header="{ title }">
              <h3>{{ title }}</h3>
            </template>
            <template #default="{ value }">
              <p>当前值: {{ value }}</p>
            </template>
          </ArticleList>
        </div>
      </section>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ArticleList } from './components/ArticleList'
import { HeroSlide } from './components/HeroSlide'

// 为 ArticleList 组件提供响应式数据
const articleValue = ref('Hello World')
</script>

<style scoped>
.app {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
}

.header h1 {
  margin: 0 0 10px 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.main {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.component-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.component-section h2 {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 1.5rem;
  font-weight: 500;
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 10px;
}

.component-demo {
  background: white;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
