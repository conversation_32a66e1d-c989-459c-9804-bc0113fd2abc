import { createApp } from 'vue'
import './style.css'
import './styles/variables.scss'
import 'virtual:uno.css'
import App from './App.vue'
import router from './router'
import ArcoVue from '@arco-design/web-vue'
import ArcoVueIcon from '@arco-design/web-vue/es/icon';
import '@arco-design/web-vue/dist/arco.css'

const app = createApp(App)
app.use(router)
app.use(ArcoVue)
app.use(ArcoVueIcon)
app.mount('#app')

// 初始化主题系统
import { useTheme } from './composables/useTheme'
const { initTheme } = useTheme()
initTheme()
