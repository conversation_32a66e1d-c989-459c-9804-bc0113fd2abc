<template>
  <button 
    :class="[
      'btn',
      `btn--${variant}`,
      `btn--${size}`,
      {
        'btn--loading': loading,
        'btn--disabled': disabled
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <span v-if="loading" class="btn__loading">
      <svg class="btn__spinner" viewBox="0 0 24 24">
        <circle 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          stroke-width="4" 
          fill="none"
          stroke-dasharray="31.416"
          stroke-dashoffset="31.416"
        />
      </svg>
    </span>
    
    <span v-if="icon && !loading" class="btn__icon">
      <slot name="icon">{{ icon }}</slot>
    </span>
    
    <span class="btn__text">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'small' | 'medium' | 'large'
  text?: string
  icon?: string
  loading?: boolean
  disabled?: boolean
}

export interface ButtonEmits {
  click: [event: MouseEvent]
}

const props = withDefaults(defineProps<ButtonProps>(), {
  variant: 'primary',
  size: 'medium',
  text: '',
  icon: '',
  loading: false,
  disabled: false
})

const emit = defineEmits<ButtonEmits>()

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
// 使用 @ 路径别名导入样式
@import '@/styles/variables.scss';

.btn {
  @include button-base;
  position: relative;
  
  // 变体样式
  &--primary {
    @include button-primary;
  }
  
  &--secondary {
    @include button-secondary;
  }
  
  &--outline {
    @include button-outline;
  }
  
  &--ghost {
    background-color: transparent;
    color: $text-primary;
    border-color: transparent;
    
    &:hover:not(:disabled) {
      background-color: $gray-100;
    }
    
    &:focus {
      box-shadow: 0 0 0 3px rgba($gray-500, 0.3);
    }
  }
  
  &--danger {
    background-color: $error-color;
    color: white;
    
    &:hover:not(:disabled) {
      background-color: darken($error-color, 10%);
    }
    
    &:focus {
      box-shadow: 0 0 0 3px rgba($error-color, 0.3);
    }
  }
  
  // 尺寸样式
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-xs;
    border-radius: $radius-sm;
  }
  
  &--medium {
    padding: $spacing-sm $spacing-md;
    font-size: $font-size-sm;
  }
  
  &--large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-base;
    border-radius: $radius-lg;
  }
  
  // 状态样式
  &--loading {
    cursor: wait;
    
    .btn__text {
      opacity: 0.7;
    }
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  // 内部元素样式
  &__loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  
  &__spinner {
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
    
    circle {
      animation: dash 1.5s ease-in-out infinite;
    }
  }
  
  &__icon {
    margin-right: $spacing-xs;
    display: flex;
    align-items: center;
    
    &:only-child {
      margin-right: 0;
    }
  }
  
  &__text {
    display: flex;
    align-items: center;
  }
  
  // 响应式样式
  @include mobile {
    &--large {
      padding: $spacing-sm $spacing-md;
      font-size: $font-size-sm;
    }
  }
}

// 动画
@keyframes spin {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

// 按钮组合样式
.btn + .btn {
  margin-left: $spacing-sm;
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .btn {
    &--ghost {
      color: white;
      
      &:hover:not(:disabled) {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}
</style>
