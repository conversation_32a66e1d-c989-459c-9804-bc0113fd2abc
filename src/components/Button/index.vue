<template>
  <div v-if="type === 'button'" class="button-primary flex-inline flex-center justify-center"  @click="handleButtonClick">
    <span class="button-text">{{ data.text }}</span>
    <span class="button-icon"></span>
  </div>
  <div v-else class="link-button flex-inline flex-center justify-center" @click="handleButtonClick">
    <span class="link-text">{{ data.text }}</span>
    <span class="button-icon link-icon"></span>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  type: {
    type: String,
    default: 'button', // button | link
  },

  data: {
    type: Object,
    default: () => {},
  },
})

// TODO: 内部跳转
const handleButtonClick = () => {
  window.open(props.data.url, '_blank')
}

console.log('data=====', props.data)
</script>

<style scoped lang="scss">
.button-icon {
  width: 18px;
  height: 18px;
  border-radius: $radius-full;
  overflow: hidden;
  background-color: $bg-primary;
  position: relative;
  margin-left: $spacing-xs;
  transition: background-color $transition-normal;
  flex-shrink: 0;

  &::before, &::after { 
    content: "";
    width: 6px;
    height: 9px;
    position: absolute;
    top: 50%;
    left: 57%;
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI5IiB2aWV3Qm94PSIwIDAgNiA5IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4yMzUxOTYgNy42MjA3OUwzLjM2MDE4IDQuNDk1OEwwLjIzNTE5NiAxLjM3MDgyQzAuMTYwNjMgMS4yOTYyNSAwLjEwMTQ4MSAxLjIwNzczIDAuMDYxMTI1OSAxLjExMDMxQzAuMDIwNzcxIDEuMDEyODggMS41NzEzN2UtMDkgMC45MDg0NiAwIDAuODAzMDA4Qy0xLjU3MTM2ZS0wOSAwLjY5NzU1NSAwLjAyMDc3MSAwLjU5MzEzNSAwLjA2MTEyNTkgMC40OTU3MUMwLjEwMTQ4MSAwLjM5ODI4NSAwLjE2MDYzIDAuMzA5NzYyIDAuMjM1MTk2IDAuMjM1MTk1QzAuMzA5NzYyIDAuMTYwNjI5IDAuMzk4Mjg1IDAuMTAxNDggMC40OTU3MSAwLjA2MTEyNTFDMC41OTMxMzYgMC4wMjA3NzAyIDAuNjk3NTU2IC0xLjU3MTM3ZS0wOSAwLjgwMzAwOCAwQzAuOTA4NDYxIDEuNTcxMzdlLTA5IDEuMDEyODggMC4wMjA3NzAyIDEuMTEwMzEgMC4wNjExMjUxQzEuMjA3NzMgMC4xMDE0OCAxLjI5NjI1IDAuMTYwNjI5IDEuMzcwODIgMC4yMzUxOTVMNS4wNjc2NCAzLjkzMjAyQzUuMzgxNzUgNC4yNDYxMyA1LjM4MTc1IDQuNzUzNTMgNS4wNjc2NCA1LjA2NzY0TDEuMzcwODIgOC43NjQ0NkMxLjI5NjMxIDguODM5MTMgMS4yMDc4IDguODk4MzYgMS4xMTAzNyA4LjkzODc4QzEuMDEyOTQgOC45NzkyIDAuOTA4NDkxIDkgMC44MDMwMDggOUMwLjY5NzUyNSA5IDAuNTkzMDc4IDguOTc5MiAwLjQ5NTY0NSA4LjkzODc4QzAuMzk4MjEyIDguODk4MzYgMC4zMDk3MDcgOC44MzkxMyAwLjIzNTE5NiA4Ljc2NDQ2Qy0wLjA3MDg1OTEgOC40NTAzNSAtMC4wNzg5MTMyIDcuOTM0ODkgMC4yMzUxOTYgNy42MjA3OVoiIGZpbGw9IiMwMDFFNjAiLz4KPC9zdmc+Cgo=);
    background-position: 50%;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: hsla(0,0%,100%,0);
    transition: transform .3s ease-in-out
  }

  &::before {
    transform: translate(-260%,-50%)
  }

  &::after {
    transform: translate(-50%,-50%)
  }
}

.link-icon {
  background-color: $primary-color;

  &::before, &::after { 
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI5IiB2aWV3Qm94PSIwIDAgNiA5IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4yMzUxOTYgNy42MjA3OUwzLjM2MDE4IDQuNDk1OEwwLjIzNTE5NiAxLjM3MDgyQzAuMTYwNjMgMS4yOTYyNSAwLjEwMTQ4MSAxLjIwNzczIDAuMDYxMTI1OSAxLjExMDMxQzAuMDIwNzcxIDEuMDEyODggMS41NzEzN2UtMDkgMC45MDg0NiAwIDAuODAzMDA4Qy0xLjU3MTM2ZS0wOSAwLjY5NzU1NSAwLjAyMDc3MSAwLjU5MzEzNSAwLjA2MTEyNTkgMC40OTU3MUMwLjEwMTQ4MSAwLjM5ODI4NSAwLjE2MDYzIDAuMzA5NzYyIDAuMjM1MTk2IDAuMjM1MTk1QzAuMzA5NzYyIDAuMTYwNjI5IDAuMzk4Mjg1IDAuMTAxNDggMC40OTU3MSAwLjA2MTEyNTFDMC41OTMxMzYgMC4wMjA3NzAyIDAuNjk3NTU2IC0xLjU3MTM3ZS0wOSAwLjgwMzAwOCAwQzAuOTA4NDYxIDEuNTcxMzdlLTA5IDEuMDEyODggMC4wMjA3NzAyIDEuMTEwMzEgMC4wNjExMjUxQzEuMjA3NzMgMC4xMDE0OCAxLjI5NjI1IDAuMTYwNjI5IDEuMzcwODIgMC4yMzUxOTVMNS4wNjc2NCAzLjkzMjAyQzUuMzgxNzUgNC4yNDYxMyA1LjM4MTc1IDQuNzUzNTMgNS4wNjc2NCA1LjA2NzY0TDEuMzcwODIgOC43NjQ0NkMxLjI5NjMxIDguODM5MTMgMS4yMDc4IDguODk4MzYgMS4xMTAzNyA4LjkzODc4QzEuMDEyOTQgOC45NzkyIDAuOTA4NDkxIDkgMC44MDMwMDggOUMwLjY5NzUyNSA5IDAuNTkzMDc4IDguOTc5MiAwLjQ5NTY0NSA4LjkzODc4QzAuMzk4MjEyIDguODk4MzYgMC4zMDk3MDcgOC44MzkxMyAwLjIzNTE5NiA4Ljc2NDQ2Qy0wLjA3MDg1OTEgOC40NTAzNSAtMC4wNzg5MTMyIDcuOTM0ODkgMC4yMzUxOTYgNy42MjA3OVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=);
  }
}

.link-button {
  overflow: hidden;
  text-decoration: none;
  position: relative;
  z-index: 1;
  white-space: nowrap;
  cursor: pointer;
  transition: all $transition-normal;

  .link-text {
    font-weight: $font-weight-medium;
    letter-spacing: .04em;
    color: $text-important;
    transition: color $transition-normal;
  }

  &::before {
    content: "";
    display: block;
    position: absolute;
    width: 200%;
    height: 500%;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);
    transition: all $transition-normal;
    z-index: -1;
    -webkit-clip-path: circle(100% at -80% 90%);
    clip-path: circle(100% at -80% 90%)
  }

  &:hover::before {
    -webkit-clip-path: circle(100% at 50% 50%);
    clip-path: circle(100% at 50% 50%)
  }

  &:hover {
    .button-icon:before {
      transform: translate(-50%,-50%)
    }

    .button-icon:after {
      transform: translate(150%,-50%)
    }

    .button-icon:after, .button-icon:before {
      background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI5IiB2aWV3Qm94PSIwIDAgNiA5IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4yMzUxOTYgNy42MjA3OUwzLjM2MDE4IDQuNDk1OEwwLjIzNTE5NiAxLjM3MDgyQzAuMTYwNjMgMS4yOTYyNSAwLjEwMTQ4MSAxLjIwNzczIDAuMDYxMTI1OSAxLjExMDMxQzAuMDIwNzcxIDEuMDEyODggMS41NzEzN2UtMDkgMC45MDg0NiAwIDAuODAzMDA4Qy0xLjU3MTM2ZS0wOSAwLjY5NzU1NSAwLjAyMDc3MSAwLjU5MzEzNSAwLjA2MTEyNTkgMC40OTU3MUMwLjEwMTQ4MSAwLjM5ODI4NSAwLjE2MDYzIDAuMzA5NzYyIDAuMjM1MTk2IDAuMjM1MTk1QzAuMzA5NzYyIDAuMTYwNjI5IDAuMzk4Mjg1IDAuMTAxNDggMC40OTU3MSAwLjA2MTEyNTFDMC41OTMxMzYgMC4wMjA3NzAyIDAuNjk3NTU2IC0xLjU3MTM3ZS0wOSAwLjgwMzAwOCAwQzAuOTA4NDYxIDEuNTcxMzdlLTA5IDEuMDEyODggMC4wMjA3NzAyIDEuMTEwMzEgMC4wNjExMjUxQzEuMjA3NzMgMC4xMDE0OCAxLjI5NjI1IDAuMTYwNjI5IDEuMzcwODIgMC4yMzUxOTVMNS4wNjc2NCAzLjkzMjAyQzUuMzgxNzUgNC4yNDYxMyA1LjM4MTc1IDQuNzUzNTMgNS4wNjc2NCA1LjA2NzY0TDEuMzcwODIgOC43NjQ0NkMxLjI5NjMxIDguODM5MTMgMS4yMDc4IDguODk4MzYgMS4xMTAzNyA4LjkzODc4QzEuMDEyOTQgOC45NzkyIDAuOTA4NDkxIDkgMC44MDMwMDggOUMwLjY5NzUyNSA5IDAuNTkzMDc4IDguOTc5MiAwLjQ5NTY0NSA4LjkzODc4QzAuMzk4MjEyIDguODk4MzYgMC4zMDk3MDcgOC44MzkxMyAwLjIzNTE5NiA4Ljc2NDQ2Qy0wLjA3MDg1OTEgOC40NTAzNSAtMC4wNzg5MTMyIDcuOTM0ODkgMC4yMzUxOTYgNy42MjA3OVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=);
      background-position: 50%;
      background-size: cover;
      background-repeat: no-repeat;
      background-color: hsla(0,0%,100%,0);
    }
  }
}

.button-primary {
  @extend .link-button;
  background-color: $button-bg;
  padding: 11px 15px;
  min-width: 160px;
  border-radius: $radius-sm;
  box-shadow: $shadow-sm;

  .button-text {
    font-weight: $font-weight-medium;
    letter-spacing: .04em;
    color: $text-white;
    transition: color $transition-normal;
  }

  &::before {
    background: $button-hover-bg;
  }

  .button-icon:after, .button-icon:before {
    background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI5IiB2aWV3Qm94PSIwIDAgNiA5IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4yMzUxOTYgNy42MjA3OUwzLjM2MDE4IDQuNDk1OEwwLjIzNTE5NiAxLjM3MDgyQzAuMTYwNjMgMS4yOTYyNSAwLjEwMTQ4MSAxLjIwNzczIDAuMDYxMTI1OSAxLjExMDMxQzAuMDIwNzcxIDEuMDEyODggMS41NzEzN2UtMDkgMC45MDg0NiAwIDAuODAzMDA4Qy0xLjU3MTM2ZS0wOSAwLjY5NzU1NSAwLjAyMDc3MSAwLjU5MzEzNSAwLjA2MTEyNTkgMC40OTU3MUMwLjEwMTQ4MSAwLjM5ODI4NSAwLjE2MDYzIDAuMzA5NzYyIDAuMjM1MTk2IDAuMjM1MTk1QzAuMzA5NzYyIDAuMTYwNjI5IDAuMzk4Mjg1IDAuMTAxNDggMC40OTU3MSAwLjA2MTEyNTFDMC41OTMxMzYgMC4wMjA3NzAyIDAuNjk3NTU2IC0xLjU3MTM3ZS0wOSAwLjgwMzAwOCAwQzAuOTA4NDYxIDEuNTcxMzdlLTA5IDEuMDEyODggMC4wMjA3NzAyIDEuMTEwMzEgMC4wNjExMjUxQzEuMjA3NzMgMC4xMDE0OCAxLjI5NjI1IDAuMTYwNjI5IDEuMzcwODIgMC4yMzUxOTVMNS4wNjc2NCAzLjkzMjAyQzUuMzgxNzUgNC4yNDYxMyA1LjM4MTc1IDQuNzUzNTMgNS4wNjc2NCA1LjA2NzY0TDEuMzcwODIgOC43NjQ0NkMxLjI5NjMxIDguODM5MTMgMS4yMDc4IDguODk4MzYgMS4xMTAzNyA4LjkzODc4QzEuMDEyOTQgOC45NzkyIDAuOTA4NDkxIDkgMC44MDMwMDggOUMwLjY5NzUyNSA5IDAuNTkzMDc4IDguOTc5MiAwLjQ5NTY0NSA4LjkzODc4QzAuMzk4MjEyIDguODk4MzYgMC4zMDk3MDcgOC44MzkxMyAwLjIzNTE5NiA4Ljc2NDQ2Qy0wLjA3MDg1OTEgOC40NTAzNSAtMC4wNzg5MTMyIDcuOTM0ODkgMC4yMzUxOTYgNy42MjA3OVoiIGZpbGw9IiMwMDFFNjAiLz4KPC9zdmc+Cgo=) !important;
  }
}

@media(max-width: 1023.98px) {
  .button-primary {
    padding:11px 15px;
    min-width: 100px
  }
}

@media(max-width: 767.98px) {
  .button-primary {
    padding:8px 12px;
    min-width: 80px
  }
}

@media(max-width: 1023.98px) {
  .button-primary {
    padding:11px 15px;
    min-width: 100px
  }
}

@media(max-width: 767.98px) {
  .button-primary {
    padding:8px 12px;
    min-width: 80px
  }
}
</style>