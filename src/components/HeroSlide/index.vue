<template>
  <div class="hero-slide">
    <div class="slide-container">
      <div
        v-for="(slide, index) in slides"
        :key="index"
        class="slide-item"
        :class="{ active: currentSlide === index }"
      >
        <h2>{{ slide.title }}</h2>
        <p>{{ slide.description }}</p>
      </div>
    </div>
    <div v-if="showIndicators" class="slide-indicators">
      <span
        v-for="(_, index) in slides"
        :key="index"
        class="indicator"
        :class="{ active: currentSlide === index }"
        @click="goToSlide(index)"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import type { HeroSlideProps, HeroSlideEmits, SlideItem } from './type'

// 定义组件名称
defineOptions({
  name: 'HeroSlide'
})

const props = withDefaults(defineProps<HeroSlideProps>(), {
  autoPlayInterval: 3000,
  showIndicators: true,
  autoPlay: true
})

const emit = defineEmits<HeroSlideEmits>()

const currentSlide = ref(0)
const slides = ref<SlideItem[]>([
  { title: '轮播图标题 1', description: '这是第一张轮播图的描述内容' },
  { title: '轮播图标题 2', description: '这是第二张轮播图的描述内容' },
  { title: '轮播图标题 3', description: '这是第三张轮播图的描述内容' }
])

let autoPlayTimer: number | null = null

const goToSlide = (index: number) => {
  currentSlide.value = index
  emit('change', index)
}

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % slides.value.length
  emit('change', currentSlide.value)
}

const startAutoPlay = () => {
  if (props.autoPlay) {
    autoPlayTimer = window.setInterval(nextSlide, props.autoPlayInterval)
  }
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}

onMounted(() => {
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style scoped>
.hero-slide {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slide-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  text-align: center;
  padding: 20px;
  box-sizing: border-box;
}

.slide-item.active {
  opacity: 1;
}

.slide-item h2 {
  margin: 0 0 16px 0;
  font-size: 2rem;
  font-weight: 600;
}

.slide-item p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
  max-width: 600px;
}

.slide-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background 0.3s ease;
}

.indicator.active {
  background: white;
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.8);
}
</style>