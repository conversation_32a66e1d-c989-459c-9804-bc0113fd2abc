<template>
  <div class="image-container" v-if="type === 'Image'">
    <a-image class="image" :src="data.imgSrc" :preview="preview" :alt="data.alt" fit="contain" width="100%" height="100%" />
    <p class="image-desc" v-if="data.caption">{{ data.caption }}</p>
  </div>

  <div class="video-container flex-inline flex-center justify-center" v-if="type === 'Video'">
    <!-- 获取播放状态 -->
    <video class="video" ref="videoElement" :src="data.videoSrc" controls></video>
    <template v-if="!isPlaying">
      <div class="video-thumbnail flex flex-center justify-center">
        <img :src="data.imgSrc">
      </div>
      <div class="play-button flex flex-center justify-center" @click="playVideo">
        <icon-caret-right style="color: #fff" />
      </div>
    </template>
  </div>

  <div class="image-list-container" v-if="type === 'ImageList'">
    <div class="image-preview">
      <a-image class="image" :src="data.previewUrl" :preview="preview" fit="contain" width="100%" height="100%" />
    </div>
    <div class="image-list-wrapper">
      <div
        class="thumbnail__nav__prev"
        v-show="showPrev"
        @click="scrollPrev"
      >
        ‹
      </div>
      <div class="image-list" ref="imageListRef" @scroll="onScroll">
        <img
          class="image"
          v-for="item in data.imageList"
          :key="item"
          :src="item"
          alt=""
          @click="setPreviewUrl(item)"
        />
      </div>
      <div
        class="thumbnail__nav__next"
        v-show="showNext"
        @click="scrollNext"
      >
        ›
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, nextTick, onUnmounted } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'Image' // image | video | image-list
  },
  data: {
    type: Object,
    default: () => {}
  },
  // imgSrc: {
  //   type: String,
  //   default: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>'
  // },
  preview: {
    type: Boolean,
    default: true
  },
  // videoSrc: {
  //   type: String,
  //   default: 'http://mpv.videocc.net/4b964bbdf4/3/4b964bbdf481505df84cfd703c4b3043_2.mp4'
  // },
  // caption: {
  //   type: String,
  //   default: '图片'
  // },
  // imageList: {
  //   type: Array as PropType<string[]>,
  //   default: () => ['https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>', 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>']
  // }
})

const videoElement = ref<HTMLVideoElement>();
const isPlaying = ref<boolean>(false);
const playVideo = () => {
  videoElement.value?.play();
  isPlaying.value = true;
}

const previewUrl = ref<string>(props.data?.imageList?.[0] || '');
const setPreviewUrl = (url: string) => {
  previewUrl.value = url;
}

const imageListRef = ref<HTMLDivElement | null>(null)
const showPrev = ref(false)
const showNext = ref(false)

const checkScroll = () => {
  const el = imageListRef.value
  if (!el) return
  showPrev.value = el.scrollLeft > 0
  showNext.value = el.scrollLeft + el.clientWidth < el.scrollWidth
}

const scrollPrev = () => {
  const el = imageListRef.value
  if (!el) return
  el.scrollBy({ left: -el.clientWidth / 1.5, behavior: 'smooth' })
}

const scrollNext = () => {
  const el = imageListRef.value
  if (!el) return
  el.scrollBy({ left: el.clientWidth / 1.5, behavior: 'smooth' })
}

const onScroll = () => {
  checkScroll()
}

watch(() => props.data?.imageList, async () => {
  await nextTick()
  checkScroll()
})

onMounted(() => {
  window.addEventListener('resize', checkScroll)
  nextTick(checkScroll)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkScroll)
})
</script>

<style lang="scss" scoped>
.image-container {
  width: 100%;
  position: relative;

  .image {
    width: 100%;
    height: auto;
    object-fit: contain;
  }

  .image-desc {
    margin-top: 12px;
    font-size: $font-size-sm;
    line-height: $line-height-relaxed;
    letter-spacing: .01em;
  }
}

.video-container {
  position: relative;
  width: 100%;
  height: auto;
  cursor: pointer;
  overflow: hidden;

  .video {
    width: 100%;
    height: auto;
  }

  .video-thumbnail {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;

    img {
      height: 100%;
    }
  }

  .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3em;
    line-height: 1.5em;
    height: 1.63332em;
    width: 3em;
    opacity: 1;
    border: .06666em solid #fff;
    background-color: #2b333f;
    background-color: rgba(43, 51, 63, .7);
    border-radius: .3em;
    transition: all .4s;
    cursor: pointer;
  }
}

.image-list-container {
  width: 100%;

  .image-preview {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 58.33%;
    overflow: hidden;

    .image {
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      cursor: pointer;
    }
  }

  .image-list-wrapper {
    position: relative;

    .image-list {
      display: flex;
      margin-top: 12px;
      overflow-x: scroll;
      overflow-y: hidden;
      scroll-behavior: smooth;
      scrollbar-color: transparent;
      scrollbar-width: none;

      .image {
        flex-shrink: 0;
        width: 87.4px;
        height: 55px;
        cursor: pointer;
      }

      .image:not(:last-of-type) {
        margin-right: 10.6px
      }
    }

    .thumbnail__nav__prev,
    .thumbnail__nav__next {
      position: absolute;
      width: 33px;
      height: 100%;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      cursor: pointer;
    }

    .thumbnail__nav__prev {
      left: -1px;
    }

    .thumbnail__nav__prev:after {
      transform: scale(-1);
    }

    .thumbnail__nav__next {
      right: -1px;
    }
  }
}

@media(max-width: 767.98px) {
  .image-container .image {
    border-radius: 0;
  }
}

@media(max-width: 1023.98px) {
  .image-desc p {
    font-size:14px;
    line-height: 1.714;
    letter-spacing: .01em
  }
}

@media(max-width: 767.98px) {
  .video-container {
    width:100vw;
    padding: 0
  }
}

@media(max-width: 767.98px) {
  .image-list-container .image-preview {
    padding-bottom:68.75%
  }
}

@media(max-width: 1023.98px) {
  .thumbnail__nav__prev,
  .thumbnail__nav__next {
    width:28px
  }
}

@media(max-width: 767.98px) {
  .thumbnail__nav__prev,
  .thumbnail__nav__next {
    box-shadow:0 4px 4px hsla(0,0%,100%,.2)
  }
}

@media(max-width: 900px) {
  .image-list-container .image-list-wrapper .image-list .image {
    width:80px
  }
}

@media(max-width: 767.98px) {
  .image-list-container .image-list-wrapper .image-list .image {
    width:75px
  }
}
</style>