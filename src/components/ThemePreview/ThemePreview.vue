<template>
  <div class="theme-preview">
    <div class="preview-header">
      <h3>主题预览</h3>
      <p>实时预览当前主题效果</p>
    </div>

    <div class="preview-content">
      <!-- 颜色预览 -->
      <div class="color-preview">
        <h4>颜色系统</h4>
        <div class="color-swatches">
          <div 
            class="color-swatch primary"
            :style="{ backgroundColor: currentTheme.primaryColor }"
          >
            <span :style="{ color: getTextColor(currentTheme.primaryColor) }">
              Primary
            </span>
          </div>
          <div 
            class="color-swatch secondary"
            :style="{ backgroundColor: currentTheme.secondaryColor }"
          >
            <span :style="{ color: getTextColor(currentTheme.secondaryColor) }">
              Secondary
            </span>
          </div>
          <div class="color-swatch success">
            <span>Success</span>
          </div>
          <div class="color-swatch warning">
            <span>Warning</span>
          </div>
          <div class="color-swatch error">
            <span>Error</span>
          </div>
        </div>
      </div>

      <!-- 字体预览 -->
      <div class="typography-preview">
        <h4>字体系统</h4>
        <div class="font-samples">
          <div 
            class="font-sample"
            :style="{ 
              fontSize: currentTheme.fontSize,
              fontFamily: currentTheme.fontFamily 
            }"
          >
            <strong>默认字体 ({{ currentTheme.fontSize }})</strong>
            <p>这是使用当前主题字体设置的示例文本。The quick brown fox jumps over the lazy dog.</p>
          </div>
        </div>
      </div>

      <!-- 组件预览 -->
      <div class="component-preview">
        <h4>组件预览</h4>
        <div class="component-samples">
          <!-- 按钮 -->
          <div class="sample-group">
            <label>按钮</label>
            <div class="button-group">
              <button 
                class="btn btn-primary"
                :style="{ 
                  backgroundColor: currentTheme.primaryColor,
                  borderRadius: currentTheme.borderRadius,
                  fontSize: currentTheme.fontSize
                }"
              >
                主要按钮
              </button>
              <button 
                class="btn btn-secondary"
                :style="{ 
                  backgroundColor: currentTheme.secondaryColor,
                  borderRadius: currentTheme.borderRadius,
                  fontSize: currentTheme.fontSize
                }"
              >
                次要按钮
              </button>
              <button 
                class="btn btn-outline"
                :style="{ 
                  borderColor: currentTheme.primaryColor,
                  color: currentTheme.primaryColor,
                  borderRadius: currentTheme.borderRadius,
                  fontSize: currentTheme.fontSize
                }"
              >
                轮廓按钮
              </button>
            </div>
          </div>

          <!-- 卡片 -->
          <div class="sample-group">
            <label>卡片</label>
            <div 
              class="card-sample"
              :style="{ 
                borderRadius: currentTheme.borderRadius,
                padding: currentTheme.spacing
              }"
            >
              <h5 :style="{ fontSize: `calc(${currentTheme.fontSize} * 1.2)` }">
                卡片标题
              </h5>
              <p :style="{ fontSize: currentTheme.fontSize }">
                这是一个卡片组件的预览示例，展示了当前主题的样式效果。
              </p>
            </div>
          </div>

          <!-- 输入框 -->
          <div class="sample-group">
            <label>输入框</label>
            <input 
              type="text" 
              class="input-sample"
              placeholder="输入框示例"
              :style="{ 
                borderRadius: currentTheme.borderRadius,
                fontSize: currentTheme.fontSize,
                padding: `calc(${currentTheme.spacing} * 0.5) ${currentTheme.spacing}`
              }"
            >
          </div>

          <!-- 标签 -->
          <div class="sample-group">
            <label>标签</label>
            <div class="tag-group">
              <span 
                class="tag tag-primary"
                :style="{ 
                  backgroundColor: `${currentTheme.primaryColor}20`,
                  color: currentTheme.primaryColor,
                  borderRadius: `calc(${currentTheme.borderRadius} * 0.5)`,
                  fontSize: `calc(${currentTheme.fontSize} * 0.875)`,
                  padding: `calc(${currentTheme.spacing} * 0.25) calc(${currentTheme.spacing} * 0.5)`
                }"
              >
                主要标签
              </span>
              <span 
                class="tag tag-secondary"
                :style="{ 
                  backgroundColor: `${currentTheme.secondaryColor}20`,
                  color: currentTheme.secondaryColor,
                  borderRadius: `calc(${currentTheme.borderRadius} * 0.5)`,
                  fontSize: `calc(${currentTheme.fontSize} * 0.875)`,
                  padding: `calc(${currentTheme.spacing} * 0.25) calc(${currentTheme.spacing} * 0.5)`
                }"
              >
                次要标签
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 布局预览 -->
      <div class="layout-preview">
        <h4>布局预览</h4>
        <div 
          class="layout-sample"
          :style="{ gap: currentTheme.spacing }"
        >
          <div 
            class="layout-item"
            :style="{ 
              borderRadius: currentTheme.borderRadius,
              padding: currentTheme.spacing
            }"
          >
            <div class="item-header">
              <div 
                class="item-avatar"
                :style="{ 
                  backgroundColor: currentTheme.primaryColor,
                  borderRadius: currentTheme.borderRadius
                }"
              ></div>
              <div class="item-info">
                <h6 :style="{ fontSize: currentTheme.fontSize }">用户名称</h6>
                <p :style="{ fontSize: `calc(${currentTheme.fontSize} * 0.875)` }">
                  用户描述信息
                </p>
              </div>
            </div>
          </div>
          <div 
            class="layout-item"
            :style="{ 
              borderRadius: currentTheme.borderRadius,
              padding: currentTheme.spacing
            }"
          >
            <div class="item-content">
              <h6 :style="{ fontSize: currentTheme.fontSize }">内容标题</h6>
              <p :style="{ fontSize: `calc(${currentTheme.fontSize} * 0.875)` }">
                这是一段示例内容，用于展示当前主题的布局效果。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTheme } from '@/composables/useTheme'
import { ThemeUtils } from '@/utils/theme'

const { currentTheme } = useTheme()

// 获取文本颜色
const getTextColor = (backgroundColor: string) => {
  return ThemeUtils.getBestTextColor(backgroundColor)
}
</script>

<style scoped>
.theme-preview {
  border: 1px solid var(--theme-gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  background: var(--theme-gray-50);
}

.preview-header {
  margin-bottom: var(--spacing-lg);
}

.preview-header h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--theme-gray-900);
}

.preview-header p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--theme-gray-600);
}

.preview-content {
  display: grid;
  gap: var(--spacing-xl);
}

.preview-content h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--theme-gray-800);
}

/* 颜色预览 */
.color-swatches {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.color-swatch {
  height: 60px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: transform var(--transition-fast);
}

.color-swatch:hover {
  transform: translateY(-2px);
}

.color-swatch.success {
  background-color: var(--theme-success);
  color: white;
}

.color-swatch.warning {
  background-color: var(--theme-warning);
  color: white;
}

.color-swatch.error {
  background-color: var(--theme-error);
  color: white;
}

/* 字体预览 */
.font-sample p {
  margin: var(--spacing-sm) 0 0 0;
  line-height: var(--line-height-relaxed);
  color: var(--theme-gray-700);
}

/* 组件预览 */
.component-samples {
  display: grid;
  gap: var(--spacing-lg);
}

.sample-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  font-size: var(--font-size-sm);
  color: var(--theme-gray-700);
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary {
  color: white;
  border-color: transparent;
}

.btn-secondary {
  color: white;
  border-color: transparent;
}

.btn-outline {
  background: transparent;
  border-width: 1px;
}

.card-sample {
  background: white;
  border: 1px solid var(--theme-gray-200);
  box-shadow: var(--shadow-sm);
}

.card-sample h5 {
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 600;
  color: var(--theme-gray-900);
}

.card-sample p {
  margin: 0;
  color: var(--theme-gray-600);
  line-height: var(--line-height-normal);
}

.input-sample {
  width: 100%;
  max-width: 300px;
  border: 1px solid var(--theme-gray-300);
  background: white;
  color: var(--theme-gray-900);
  outline: none;
  transition: border-color var(--transition-fast);
}

.input-sample:focus {
  border-color: var(--theme-primary);
}

.tag-group {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.tag {
  display: inline-block;
  font-weight: 500;
}

/* 布局预览 */
.layout-sample {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.layout-item {
  background: white;
  border: 1px solid var(--theme-gray-200);
  box-shadow: var(--shadow-sm);
}

.item-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.item-avatar {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
}

.item-info h6 {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 600;
  color: var(--theme-gray-900);
}

.item-info p {
  margin: 0;
  color: var(--theme-gray-600);
}

.item-content h6 {
  margin: 0 0 var(--spacing-sm) 0;
  font-weight: 600;
  color: var(--theme-gray-900);
}

.item-content p {
  margin: 0;
  color: var(--theme-gray-600);
  line-height: var(--line-height-normal);
}

@media (max-width: 768px) {
  .color-swatches {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .button-group {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .layout-sample {
    grid-template-columns: 1fr;
  }
}
</style>
