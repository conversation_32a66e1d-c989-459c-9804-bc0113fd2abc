<template>
  <div class="operate-page" v-show="show">
    <div class="operate-btn flex justify-end">
      <div class="btn-item btn-copy flex items-center" @click="handleCopy">
        <icon-copy />
        <span class="btn-text">复制</span>
      </div>
      <div class="btn-item btn-delete flex items-center" @click="handleDelete">
        <icon-delete />
        <span class="btn-text">删除</span>
      </div>
      <div class="btn-item btn-edit flex items-center" @click="handleEdit">
        <icon-edit />
        <span class="btn-text">编辑</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['handleCopy', 'handleDelete', 'handleEdit'])

const handleCopy = () => {
  emit('handleCopy')
}
const handleDelete = () => {
  emit('handleDelete')
}
const handleEdit = () => {
  emit('handleEdit')
}
</script>

<style lang="scss" scoped>
.operate-page {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;

  .operate-btn {
    .btn-item {
      margin-left: 12px;
      padding: 4px 16px;
      font-size: 10px;
      color: #fff;
      border-radius: 4px;
      font-weight: 600;
      cursor: pointer;
    }

    .btn-copy {
      background: $warning-color;
    }

    .btn-delete {
      background: $error-color;
    }

    .btn-edit {
      background: $primary-color;
    }
  }
}
</style>