<template>
  <div class="rich-text-editor">
    <!-- 工具栏 -->
    <Toolbar
      class="editor-toolbar"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    
    <!-- 编辑器 -->
    <Editor
      class="editor-content"
      :style="{ height: editorHeight }"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @onDestroyed="handleDestroyed"
      @onFocus="handleFocus"
      @onBlur="handleBlur"
    />
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css'
import { onBeforeUnmount, ref, shallowRef, watch, computed } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { IDomEditor, IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

// 组件属性定义
interface Props {
  modelValue?: string
  placeholder?: string
  height?: string | number
  mode?: 'default' | 'simple'
  disabled?: boolean
  maxLength?: number
  excludeKeys?: string[]
  includeKeys?: string[]
  uploadImgServer?: string
  uploadVideoServer?: string
}

// 事件定义
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', editor: IDomEditor): void
  (e: 'focus', editor: IDomEditor): void
  (e: 'blur', editor: IDomEditor): void
  (e: 'created', editor: IDomEditor): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  height: '400px',
  mode: 'default',
  disabled: false,
  maxLength: 10000,
  excludeKeys: () => [],
  includeKeys: () => [],
  uploadImgServer: '',
  uploadVideoServer: ''
})

const emit = defineEmits<Emits>()

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef<IDomEditor>()

// 内容 HTML
const valueHtml = ref('')

// 计算编辑器高度
const editorHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  }
  return props.height
})

// 工具栏配置
const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: props.excludeKeys,
  insertKeys: {
    index: 0,
    keys: props.includeKeys
  }
}

// 编辑器配置
const editorConfig: Partial<IEditorConfig> = {
  placeholder: props.placeholder,
  readOnly: props.disabled,
  maxLength: props.maxLength,
  MENU_CONF: {
    // 上传图片配置
    uploadImage: {
      server: props.uploadImgServer,
      fieldName: 'file',
      maxFileSize: 5 * 1024 * 1024, // 5M
      allowedFileTypes: ['image/*'],
      meta: {},
      headers: {},
      withCredentials: false,
      timeout: 30 * 1000, // 30s
      onBeforeUpload(file: File) {
        console.log('onBeforeUpload', file)
        return file
      },
      onProgress(progress: number) {
        console.log('onProgress', progress)
      },
      onSuccess(file: File, res: any) {
        console.log('onSuccess', file, res)
      },
      onFailed(file: File, res: any) {
        console.log('onFailed', file, res)
      },
      onError(file: File, err: any) {
        console.log('onError', file, err)
      }
    },
    // 上传视频配置
    uploadVideo: {
      server: props.uploadVideoServer,
      fieldName: 'file',
      maxFileSize: 50 * 1024 * 1024, // 50M
      allowedFileTypes: ['video/*'],
      meta: {},
      headers: {},
      withCredentials: false,
      timeout: 60 * 1000, // 60s
      onBeforeUpload(file: File) {
        console.log('onBeforeUpload video', file)
        return file
      },
      onProgress(progress: number) {
        console.log('onProgress video', progress)
      },
      onSuccess(file: File, res: any) {
        console.log('onSuccess video', file, res)
      },
      onFailed(file: File, res: any) {
        console.log('onFailed video', file, res)
      },
      onError(file: File, err: any) {
        console.log('onError video', file, err)
      }
    }
  }
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

// 监听外部传入的内容变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal !== valueHtml.value) {
      valueHtml.value = newVal || ''
    }
  },
  { immediate: true }
)

// 编辑器回调函数
const handleCreated = (editor: IDomEditor) => {
  editorRef.value = editor
  emit('created', editor)
}

const handleChange = (editor: IDomEditor) => {
  emit('update:modelValue', valueHtml.value)
  emit('change', editor)
}

const handleDestroyed = () => {
  editorRef.value = undefined
}

const handleFocus = (editor: IDomEditor) => {
  emit('focus', editor)
}

const handleBlur = (editor: IDomEditor) => {
  emit('blur', editor)
}

// 暴露编辑器实例和方法
defineExpose({
  editor: editorRef,
  getHtml: () => valueHtml.value,
  getText: () => editorRef.value?.getText() || '',
  isEmpty: () => editorRef.value?.isEmpty() || true,
  clear: () => editorRef.value?.clear(),
  focus: () => editorRef.value?.focus(),
  blur: () => editorRef.value?.blur(),
  disable: () => editorRef.value?.disable(),
  enable: () => editorRef.value?.enable()
})
</script>

<style scoped>
.rich-text-editor {
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}

.editor-toolbar {
  border-bottom: 1px solid var(--color-border-2);
  background: var(--color-bg-1);
}

.editor-content {
  overflow-y: auto;
}

/* 编辑器内容样式 */
:deep(.w-e-text-container) {
  background: #fff;
}

:deep(.w-e-text-placeholder) {
  color: var(--color-text-3);
}

/* 工具栏样式调整 */
:deep(.w-e-toolbar) {
  border-bottom: none;
  background: transparent;
}

:deep(.w-e-toolbar .w-e-bar-item button) {
  border-radius: 4px;
  transition: all 0.2s;
}

:deep(.w-e-toolbar .w-e-bar-item button:hover) {
  background: var(--color-fill-2);
}

:deep(.w-e-toolbar .w-e-bar-item button.active) {
  background: rgb(var(--primary-1));
  color: rgb(var(--primary-6));
}

/* 禁用状态样式 */
.rich-text-editor.disabled {
  background: var(--color-bg-2);
  cursor: not-allowed;
}

.rich-text-editor.disabled :deep(.w-e-text-container) {
  background: var(--color-bg-2);
  cursor: not-allowed;
}
</style>
