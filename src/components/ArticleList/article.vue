<template>
  <div class="article-item">
    <h4 class="article-item-title">{{ article.title }}</h4>
    <div class="editor-content" v-html="article.content"></div>
    <div class="article-item-button">
      <Button v-for="item in article.buttonList" :key="item.id" :data="item"></Button>
    </div>
    <div class="article-item-link">
      <Button v-for="item in article.linkList" :key="item.id" :data="item" type="link"></Button>
    </div>
  </div>
</template>

<script lang="ts" setup name="Article">
import Button from '@/components/Button/index.vue' 
 
defineProps({
  article: {
    type: Object,
    default: () => {}
  }
})
</script>

<style scoped lang="scss">
@use '../../styles/editor.scss' as *;
.article-item {
  --max-width: 952px;
  margin: 0 auto;
  max-width: calc(var(--max-width));

  .article-item-title {
    font-size: 26px;
    color: $text-important;
  }

  .article-item-button {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
  }

  .article-item-link {
    display: flex;
    margin-top: 16px;
  }
}

@media(max-width: 767.98px) {
  .article-item-title {
    font-size: 24px;
    font-weight: 500;
    line-height: 1.5;
  }

  .article-item-button {
    flex-direction:column;
    align-items: flex-start;
  }

  .article-item-link {
    margin-top: 12px;
  }
}

@media(max-width: 767.98px)and (max-width:767.98px) {
  .article-item-title {
    font-size: 20px;
    line-height: 1.2;
    letter-spacing: -.005em;
  }
}
</style>