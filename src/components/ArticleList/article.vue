<template>
  <div class="article-item">
    <p class="article-item-title">{{ article.title }}</p>
    <div class="article-item-content" v-html="article.content"></div>
    <div class="article-item-button">
      <Button v-for="item in article.buttonList" :key="item.id" @click="handleButtonClick(item)">{{ item.text }}</Button>
    </div>
    <div class="article-item-link">
      <Button v-for="item in article.linkList" :key="item.id" type="link" @click="handleButtonClick(item)">{{ item.text }}</Button>
    </div>
  </div>
</template>

<script lang="ts" setup name="Article">
import Button from '@/components/Button/index.vue' 
 
defineProps({
  article: {
    type: Object,
    default: () => {}
  }
})

// TODO: 内部跳转
const handleButtonClick = (item: any) => {
  window.open(item.url, '_blank')
}
</script>

<style scoped lang="scss">
.article-item {
  --max-width: 952px;
  padding-left: 36px;
  padding-right: 36px;
  margin: 0 auto;
  max-width: calc(var(--max-width));

  .article-item-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

@media(max-width: 1023.98px) {
  .container-content,.container-content-952 {
      padding-left:24px;
      padding-right: 24px;
      max-width: calc(var(--max-width) + 48px)
  }
}

@media(max-width: 767.98px) {
  .container-content,.container-content-952 {
      padding-left:12px;
      padding-right: 12px;
      max-width: calc(var(--max-width) + 24px)
  }
}

@media(max-width: 575.98px) {
  .container-content,.container-content-952 {
      padding-left:12px;
      padding-right: 12px;
      max-width: calc(var(--max-width) + 24px)
  }
}
</style>