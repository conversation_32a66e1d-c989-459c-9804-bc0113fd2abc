<template>
  <div class="contact-item">
    <div class="contact-item-categories">
      <p class="categories" v-for="item in contact.categories" :key="item.id">{{ item.text }}</p>
    </div>
    <h4 class="contact-item-title">{{ contact.title }}</h4>
    <div class="editor-content" v-html="contact.content"></div>
    <div class="contact-item-link">
      <Button v-for="item in contact.linkList" :key="item.id" :data="item" type="link"></Button>
    </div>
  </div>
</template>

<script lang="ts" setup name="Contact">
import Button from '@/components/Button/index.vue'

defineProps({
  contact: {
    type: Object,
    default: () => {}
  }
})
</script>

<style lang="scss" scoped> 
@use '../../styles/editor.scss' as *;
.contact-item {
  max-width: 952px;
  margin: 0 auto;

  .contact-item-categories {
    display: flex;
    align-items: center;
    margin-top: 16px;

    &::not(:last-child) {
      margin-right: 24px;
    }

    .categories {
      color: $primary-600;
    }
  }
}

.contact-item-title {
  margin-top: 8px;
  font-size: 20px;
  line-height: 1.3;
  color: $text-important;
}

.contact-item-link {
  display: flex;
  margin-top: 12px;
}
</style>

<style lang="scss">
@media(max-width: 1023.98px) {
  .contact-item {
    max-width: 1000px;
    padding-left:24px;
    padding-right: 24px;
  }
}

@media(max-width: 767.98px) {
  .contact-item {
    padding-left:12px;
    padding-right: 12px;
  }

  .contact-item-title {
    font-size:24px;
    font-weight: 500;
    line-height: 1.5;
    margin-top: 12px;
  }
}

@media(max-width: 575.98px) {
  .contact-item {
    padding-left:12px;
    padding-right: 12px;
    max-width: 976px;
  }

  .contact-item-title {
    font-size:20px;
    line-height: 1.2;
    letter-spacing: -.005em;
  }
}
</style>