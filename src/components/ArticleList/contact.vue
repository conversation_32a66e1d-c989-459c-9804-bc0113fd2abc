<template>
  <div class="contact-item">
    <div class="contact-item-categories">
      <p class="categories" v-for="item in contact.categories" :key="item.id">{{ item.text }}</p>
    </div>
    <p class="contact-item-title">{{ contact.title }}</p>
    <div class="contact-item-content" v-html="contact.content"></div>
    <div class="contact-item-link">
      <Button v-for="item in contact.linkList" :key="item.id" type="link" @click="handleButtonClick(item)">{{ item.text }}</Button>
    </div>
  </div>
</template>

<script lang="ts" setup name="Contact">
import Button from '@/components/Button/index.vue'

defineProps({
  contact: {
    type: Object,
    default: () => {}
  }
})

// TODO: 内部跳转
const handleButtonClick = (item: any) => {
  window.open(item.url, '_blank')
}
</script>