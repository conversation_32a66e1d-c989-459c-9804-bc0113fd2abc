<template>
  <div 
    class="your-component"
    :class="[`size-${size}`, { 'is-disabled': disabled }]"
    @click="handleClick"
  >
    <slot name="header" :title="title"></slot>
    <div class="content">
      <slot :value="modelValue">{{ modelValue }}</slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { ComponentProps, ComponentEmits, ComponentSlots } from './type'

// 定义组件名称
defineOptions({
  name: 'ArticleList'
})

const props = withDefaults(defineProps<ComponentProps>(), {
  size: 'medium',
  disabled: false
})

const emit = defineEmits<ComponentEmits>()
defineSlots<ComponentSlots>()

const title = computed(() => `Current value: ${props.modelValue}`)

function handleClick() {
  if (!props.disabled) {
    emit('change', props.modelValue)
  }
}
</script>

<style scoped>
.your-component {
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.your-component:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.your-component.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.your-component.size-small {
  padding: 8px;
  font-size: 12px;
}

.your-component.size-medium {
  padding: 16px;
  font-size: 14px;
}

.your-component.size-large {
  padding: 24px;
  font-size: 16px;
}

.content {
  margin-top: 8px;
}
</style>