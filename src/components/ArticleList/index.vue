<template>
  <div class="article-list-container" :style="{ background: modelValue?.background ? modelValue?.background === 'white' ? '#fff' : '#F7F7FA' : 'transparent' }"> 
    <a-row class="article-list" @mouseenter="isHover = true" @mouseleave="isHover = false">
      <!-- 左侧文本 后侧媒体 -->
      <!-- 左侧一直是一半，但只有左侧时是100% -->
      <a-col :sm="{span: 24}" :md="{span: hasMedia ? 12 : 24}">
        <div class="article-list-left">
          <template v-for="item in modelValue?.data" :key="item.id">
            <Article v-if="item.type === 'Article'" :article="item.data"></Article>
            <Contact v-if="item.type === 'Contact'" :contact="item.data"></Contact>
          </template>
        </div>
      </a-col>
      <a-col v-if="hasMedia" :sm="{span: 24}" :md="{span: 12}">
        <div class="article-list-right"> 
          <template v-for="item in modelValue?.data" :key="item.id">
            <Media :type="item.type" :data="item.data" :preview="modelValue.isPreview"></Media>
          </template>
        </div>
      </a-col>

      <!-- <div v-if="isHover" class="add-btn">
        <icon-plus />
        <span class="btn-text">添加组件</span>
      </div> -->
      <Operate v-model:show="isHover" @handle-edit="showSetting = true" @handle-delete="handleDelete" @handle-copy="handleCopy"></Operate>
    </a-row>
  </div>


  <Setting v-model:show="showSetting" :data="modelValue"></Setting>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { randomString } from '@/utils/common'
import Article from './article.vue'
import Contact from './contact.vue'
import Media from '@/components/Media/index.vue'
import Setting from './setting.vue'
import Operate from '@/components/Operate/index.vue'

// 定义组件名称
defineOptions({
  name: 'ArticleList'
})

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {}
  },

  // 是否预览
  isPreview: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'handleDelete', 'handleCopy'])

const hasMedia = computed(() => {
  if (!(props.modelValue && props.modelValue.data && props.modelValue.data.length)) return false
  return props.modelValue.data.some((item: any) => item.type === 'Image' || item.type === 'Video' || item.type === 'ImageList')
})

const handleInit = () => {
  if (props.modelValue && props.modelValue.id) return // 有数据不用初始化
  const data = {
    id: randomString(),
    type: 'ArticleList',
    isPreview: false,
    width: '',
    background: '',
    data: [
      {
        id: randomString(),
        type: 'Article',
        data: {
          title: '这里是标题',
          content: '耀华国际教育学校浙江桐乡校区成立于 2017 年，先进的校园设施确保学 生在安全友好的环境中学习。学校坐落于高桥镇，提供从幼儿阶段至高中 阶段 (K2 至13 年级) 的教育服务，招收2 岁至18 岁本地和外籍学生。',
          buttonList: [
            {
              id: randomString(),
              text: '查看更多',
              url: 'https://www.baidu.com',
              isExternal: false
            }
          ],
          linkList: [
            {
              id: randomString(),
              text: '查看更多',
              url: 'https://www.baidu.com',
              isExternal: false
            }
          ]
        }
      },
      {
        id: randomString(),
        type: 'Image',
        data: {
          imgSrc: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>',
          caption: '',
          alt: '',
          isRound: false
        }
      },
      {
        id: randomString(),
        type: 'Contact',
        data: {
          title: '这里是标题',
          content: '耀华国际教育学校浙江桐乡校区成立于 2017 年，先进的校园设施确保学 生在安全友好的环境中学习。学校坐落于高桥镇，提供从幼儿阶段至高中 阶段 (K2 至13 年级) 的教育服务，招收2 岁至18 岁本地和外籍学生。',
          categories: [
            {
              id: randomString(),
              text: '分类1'
            }
          ],
          linkList: [
            {
              id: randomString(),
              text: '查看更多',
              url: 'https://www.baidu.com',
              isExternal: false
            }
          ]
        }
      },
      {
        id: randomString(),
        type: 'ImageList',
        data: {
          imageList: [
            {
              id: randomString(),
              src: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>'
            }
          ],
        }
      },
      {
        id: randomString(),
        type: 'Video',
        data: {
          videoSrc: 'http://mpv.videocc.net/4b964bbdf4/3/4b964bbdf481505df84cfd703c4b3043_2.mp4',
          imgSrc: 'https://object.ycyw.com/media-library/ycyw-edu/news/20250628%20HKDSE%20Forum/Cover.jpg',
        }
      },
    ]
  }
  emit('update:modelValue', data)
} 

const isHover = ref<boolean>(false);
const showSetting = ref<boolean>(false);

const handleDelete = () => {
  emit('handleDelete', props.modelValue.id)
}

const handleCopy = () => {
  emit('handleCopy', props.modelValue)
}

handleInit()
</script>

<style lang="scss" scoped>
/* ===== 响应式设计 - 桌面优先，确保正确的层叠顺序 ===== */

/* 基础样式 - 桌面端 (1024px 及以上) */

.article-list-container {
  width: 100%;
}
.article-list {
  position: relative;
  max-width: 952px;
  padding: 16px 36px;
  margin: 0 auto;

  // &:hover {
  //   border: 2px solid $primary-color;
  // }

  .add-btn {
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate(-50%, 50%);

    padding: 4px 8px;
    font-size: 10px;
    background: $primary-color;
    color: #fff;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
  }
}

.article-list-right {
  margin-left: 60px;
  max-width: 409px;
}

/* 平板设备 (768px - 1023px) */
@media (max-width: 1023.98px) {
  .article-list {
    padding: 12px 24px;
    max-width: 1000px;
  }

  .article-list-right {
    justify-content: flex-end;
    margin-left: 20px;
    max-width: 320px;
  }
}

/* 小平板和大手机 (576px - 767px) */
@media (max-width: 767.98px) {
  .article-list {
    padding: 8px 12px;
    max-width: 976px;
  }

  .article-list-right {
    justify-content: flex-start;
    margin-left: 0;
    max-width: 100vw;
  }
}

/* 小手机 (575px 及以下) */
@media (max-width: 575.98px) {
  .article-list {
     padding: 8px 12px;
    max-width: 100%;
  }

  .article-list-right {
    justify-content: flex-start;
    margin-left: 0;
    max-width: 100%;
  }
}
</style>