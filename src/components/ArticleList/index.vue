<template>
  <a-row class="article-list">
    <!-- 左侧文本 后侧媒体 -->
     <!-- 左侧一直是一半，但只有左侧时是100% -->
    <a-col :sm="{span: 24}" :md="{span: hasMedia ? 12 : 24}">
      <template v-for="item in props.value.data" :key="item.id">
        <Article v-if="item.type === 'Article'" :article="item.data"></Article>
        <Contact v-if="item.type === 'Contact'" :contact="item.data"></Contact>
      </template>
    </a-col>
    <a-col v-if="hasMedia" :sm="{span: 24}" :md="{span: 12}">
      <template v-for="item in props.value.data" :key="item.id">
        <Media :type="item.type" :data="item.data" :preview="value.isPreview"></Media>
      </template>
    </a-col>
  </a-row>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { randomString } from '@/utils/common'
import Article from './article.vue'
import Contact from './contact.vue'
import Media from '@/components/Media/index.vue'

// 定义组件名称
defineOptions({
  name: 'ArticleList'
})

const props = defineProps({
  value: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:value'])

const hasMedia = computed(() => {
  if (!(props.value && props.value.data && props.value.data.length)) return false
  return props.value.data.some((item: any) => item.type === 'Image' || item.type === 'Video' || item.type === 'ImageList')
})

const handleInit = () => {
  if (props.value && props.value.id) return // 有数据不用初始化
  const data = {
    id: randomString(),
    type: 'ArticleList',
    isPreview: false,
    data: [
      {
        id: randomString(),
        type: 'Article',
        data: {
          title: '这里是标题',
          content: '耀华国际教育学校浙江桐乡校区成立于 2017 年，先进的校园设施确保学 生在安全友好的环境中学习。学校坐落于高桥镇，提供从幼儿阶段至高中 阶段 (K2 至13 年级) 的教育服务，招收2 岁至18 岁本地和外籍学生。',
          buttonList: [
            {
              id: randomString(),
              text: '查看更多',
              url: 'https://www.baidu.com'
            }
          ]
        }
      },
      {
        id: randomString(),
        type: 'Image',
        data: {
          imgSrc: 'https://osswebsite.ycyw.com/media-library/ywies-bj/images/home/<USER>',
          caption: '',
          alt: '',
          borderStyle: null
        }
      }
    ]
  }
  emit('update:value', data)
} 

handleInit()
</script>

<style scoped>
.your-component {
  padding: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.your-component:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.your-component.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.your-component.size-small {
  padding: 8px;
  font-size: 12px;
}

.your-component.size-medium {
  padding: 16px;
  font-size: 14px;
}

.your-component.size-large {
  padding: 24px;
  font-size: 16px;
}

.content {
  margin-top: 8px;
}
</style>