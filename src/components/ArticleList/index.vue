<template>
  <div 
    class="your-component"
    :class="[`size-${size}`, { 'is-disabled': disabled }]"
    @click="handleClick"
  >
    <slot name="header" :title="title"></slot>
    <div class="content">
      <slot :value="modelValue">{{ modelValue }}</slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { ComponentProps, ComponentEmits, ComponentSlots } from './type'

const props = withDefaults(defineProps<ComponentProps>(), {
  size: 'medium',
  disabled: false
})

const emit = defineEmits<ComponentEmits>()
defineSlots<ComponentSlots>()

const title = computed(() => `Current value: ${props.modelValue}`)

function handleClick() {
  if (!props.disabled) {
    emit('change', props.modelValue)
  }
}
</script>