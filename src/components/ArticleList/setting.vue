<template>
  <a-drawer :width="500" :visible="show" :footer="false" @cancel="handleCancel" unmountOnClose>
    <template #title>
      {{data.type}}组件编辑
    </template>
    <div class="setting-content">
      <!-- 组件自身属性 -->
      <div class="setting-header flex items-center">
        <span class="header-title">组件宽度</span>
        <a-select v-model="data.width" placeholder="请选择" allow-clear>
          <a-option value="default">默认</a-option>
          <a-option value="small">小</a-option>
          <a-option value="medium">中</a-option>
          <a-option value="large">大</a-option>
        </a-select>
      </div>
      <div class="setting-header flex items-center">
        <span class="header-title">组件背景</span>
        <a-select v-model="data.background" placeholder="请选择" allow-clear>
          <a-option value="white">白色</a-option>
          <a-option value="grey">灰色</a-option>
        </a-select>
      </div>

      <a-button-group type="primary">
        <a-button size="mini" @click="handleAdd('Article')"><template #icon><icon-plus /></template> Article </a-button>
        <a-button size="mini" @click="handleAdd('Contact')"><template #icon><icon-plus /></template> Contact </a-button>
        <a-button size="mini" @click="handleAdd('Image')"><template #icon><icon-plus /></template> Image </a-button>
        <a-button size="mini" @click="handleAdd('ImageList')"><template #icon><icon-plus /></template> ImageList </a-button>
        <a-button size="mini" @click="handleAdd('Video')"><template #icon><icon-plus /></template> Video </a-button>
      </a-button-group>

      <!-- 子组件属性 -->
      <div class="setting-body">
        <div class="setting-item" v-for="item in data.data" :key="item.id">
          <p class="item-name">{{ item.type }}</p>

          <template v-if="item.type === 'Article'">
            <p class="item-title">标题</p>
            <a-input v-model="item.data.title" placeholder="请输入标题" allow-clear />

            <p class="item-title">内容</p>
            <RichTextEditor v-model="item.data.content"></RichTextEditor>

            <p class="item-title">按钮</p>
            <div class="item-button" v-for="button in item.data.buttonList" :key="button.id">
              <icon-delete class="btn-delete" @click="handleDeleteButton(item.data.buttonList, button.id)" />
              <a-button type="primary">{{ button.text }}</a-button>
              <div class="item-action flex items-center">
                <a-input class="action-text" v-model="button.text" placeholder="按钮文本" allow-clear />
                <a-input v-model="button.url" placeholder="按钮链接" allow-clear />
              </div>
              <a-checkbox :model-value="button.isExternal">是否外部链接</a-checkbox>
            </div>
            <div class="item-add-btn flex items-center" @click="handleAddButton(item.data.buttonList)">
              <icon-plus />
              <span class="btn-text">添加按钮</span>
            </div>

            <p class="item-title">链接</p>
            <div class="item-button" v-for="link in item.data.linkList" :key="link.id">
              <icon-delete class="btn-delete" @click="handleDeleteLink(item.data.linkList, link.id)" />
              <a-link href="link">{{ link.text }}</a-link>
              <div class="item-action flex items-center">
                <a-input class="action-text" v-model="link.text" placeholder="按钮文本" allow-clear />
                <a-input v-model="link.url" placeholder="按钮链接" allow-clear />
              </div>
              <a-checkbox :model-value="link.isExternal">是否外部链接</a-checkbox>
            </div>
            <div class="item-add-btn flex items-center" @click="handleAddLink(item.data.linkList)">
              <icon-plus />
              <span class="btn-text">添加链接</span>
            </div>
          </template>

          <template v-if="item.type === 'Contact'">
            <p class="item-title">标题</p>
            <a-input v-model="item.data.title" placeholder="请输入标题" allow-clear />

            <p class="item-title">内容</p>
            <RichTextEditor v-model="item.data.content"></RichTextEditor>

            <p class="item-title">分类</p>
            <div class="item-button" v-for="category in item.data.categories" :key="category.id">
              <icon-delete class="btn-delete" @click="handleDeleteCategory(item.data.categories, category.id)" />
              <a-link href="link">{{ category.text }}</a-link>
              <div class="item-action flex items-center">
                <a-input v-model="category.text" placeholder="分类名称" allow-clear />
              </div>
            </div>
            <div class="item-add-btn flex items-center" @click="handleAddCategory(item.data.categories)">
              <icon-plus />
              <span class="btn-text">添加分类</span>
            </div>

            <p class="item-title">链接</p>
            <div class="item-button" v-for="link in item.data.linkList" :key="link.id">
              <icon-delete class="btn-delete" @click="handleDeleteLink(item.data.linkList, link.id)" />
              <a-link href="link">{{ link.text }}</a-link>
              <div class="item-action flex items-center">
                <a-input class="action-text" v-model="link.text" placeholder="按钮文本" allow-clear />
                <a-input v-model="link.url" placeholder="按钮链接" allow-clear />
              </div>
              <a-checkbox :model-value="link.isExternal">是否外部链接</a-checkbox>
            </div>
            <div class="item-add-btn flex items-center" @click="handleAddLink(item.data.linkList)">
              <icon-plus />
              <span class="btn-text">添加链接</span>
            </div>
          </template>

          <template v-if="item.type === 'Image'">
            <img class="item-img" :src="item.data.imgSrc" />
            <div class="item-uplaod flex items-center">
              <a-input v-model="item.data.imgSrc" placeholder="请输入标题" allow-clear />
              <a-upload class="fit-content" :show-file-list="false" action="/" />
            </div>
            <a-input class="mt-10" v-model="item.data.caption" placeholder="图片描述" allow-clear />
            <a-input class="mt-10" v-model="item.data.alt" placeholder="图片alt" allow-clear />
            <a-checkbox class="pt-10" :model-value="item.data.isRound">圆角</a-checkbox>
          </template>

          <template v-if="item.type === 'ImageList'">
            <p class="item-title">图片组</p>
            <div class="item-img-list">
              <div class="img-list-item flex" v-for="img in item.data.imageList" :key="img.id">
                <div class="item-img-box" v-if="img.src">
                  <icon-close-circle-fill class="item-img-dlete" @click="handleDeleteImage(item.data.imageList, img.id)" />
                  <img class="item-img small-img" :src="img.src" />
                </div>

                <div class="item-right flex-1">
                  <a-input style="margin-bottom: 12px" v-model="img.src" placeholder="请输入图片路径" allow-clear />
                  <a-upload :show-file-list="false" action="/" />
                </div>
              </div>
            </div>
            <div class="item-add-btn flex items-center" @click="handleAddImage(item.data.imageList)">
              <icon-plus />
              <span class="btn-text">添加图片</span>
            </div>          
          </template>

          <template v-if="item.type === 'Video'">
            <p class="item-title">视频</p>
            <video class="item-img" :src="item.data.videoSrc" controls></video>
            <div class="item-uplaod flex items-center">
              <a-input v-model="item.data.videoSrc" placeholder="请输入视频链接" allow-clear />
              <a-upload class="fit-content" :show-file-list="false" action="/" />
            </div>
            <img class="item-img" :src="item.data.imgSrc" />
            <div class="item-uplaod flex items-center">
              <a-input v-model="item.data.imgSrc" placeholder="视频封面" allow-clear />
              <a-upload class="fit-content" :show-file-list="false" action="/" />
            </div>
          </template>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script lang="ts" setup>
import RichTextEditor from '@/components/RichTextEditor'
import { randomString } from '@/utils/common'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },

  data: {
    type: Object,
    default: () => {}
  }
})

const emit = defineEmits(['update:show']);

const handleCancel = () => {
  emit('update:show', false);
}

const handleAdd = (type: string) => {
  const detail = props.data;
  if (type === 'Article') {
    detail.data.push({
      id: randomString(),
      type: 'Article',
      data: {
        title: '',
        content: '',
        buttonList: [],
        linkList: []
      }
    })
  } else if (type === 'Contact') {
    detail.data.push({
      id: randomString(),
      type: 'Contact',
      data: {
        title: '',
        content: '',
        categories: [],
        linkList: []
      }
    })
  } else if (type === 'Image') {
    detail.data.push({
      id: randomString(),
      type: 'Image',
      data: {
        imgSrc: '',
        caption: '',
        alt: '',
        isRound: false
      }
    })
  } else if (type === 'ImageList') {
    detail.data.push({
      id: randomString(),
      type: 'ImageList',
      data: {
        imageList: []
      }
    })
  } else if (type === 'Video') {
    detail.data.push({
      id: randomString(),
      type: 'Video',
      data: {
        videoSrc: '',
        imgSrc: ''
      }
    })
  }
}

const handleAddButton = (list: any[]) => {
  list.push({
    id: randomString(),
    text: '查看更多',
    url: 'https://www.baidu.com',
    isExternal: false
  })
}

const handleDeleteButton = (list: any[], id: string) => {
  list.splice(list.findIndex(item => item.id === id), 1)
}

const handleAddLink = (list: any[]) => {
  list.push({
    id: randomString(),
    text: '查看更多',
    url: 'https://www.baidu.com',
    isExternal: false
  })
}

const handleDeleteLink = (list: any[], id: string) => {
  list.splice(list.findIndex(item => item.id === id), 1)
}

const handleAddCategory = (list: any[]) => {
  list.push({
    id: randomString(),
    text: '分类'
  })
}

const handleDeleteCategory = (list: any[], id: string) => {
  list.splice(list.findIndex(item => item.id === id), 1)
}
const handleAddImage = (list: any[]) => {
  list.push({
    id: randomString(),
    src: ''
  })
}

const handleDeleteImage = (list: any[], img: string) => {
  list.splice(list.findIndex(item => item === img), 1)
}
</script>

<style lang="scss" scoped>
.setting-content {
  .setting-header {
    padding-bottom: 12px;

    .header-title {
      width: 120px;
      padding-right: 12px;
      text-align: right;
    }
  }

  .setting-body {
    margin-top: 12px;
    
    .setting-item {
      padding: 16px 12px;
      background: #f0f2f5;
      border-radius: 8px;
      margin-bottom: 20px;

      .item-name {
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #fff;
      }

      .item-title {
        padding: 12px 0 8px 0;
        font-size: 14px;
        font-weight: 600;
      }

      .arco-input-wrapper {
        background: #fff;
      }

      .item-button {
        position: relative;
        padding: 12px 8px 4px;
        background: #fff;
        border-radius: 4px;
        margin-bottom: 4px;

        .arco-input-wrapper {
          background: #f2f3f5;
        }

        .btn-delete {
          position: absolute;
          padding: 12px;
          top: 0;
          right: 0;
          font-size: 40px;
          cursor: pointer;
        }
      }

      .item-action {
        padding: 10px 0;

        .action-text {
          width: 150px;
          margin-right: 8px;
        }
      }

      .item-add-btn {
        display: inline-block;
        padding: 2px 8px;
        margin-top: 5px;
        font-size: 11px;
        color: #165dff;
        border-radius: 4px;
        font-weight: 600;
        border: 1px solid #165dff;
        cursor: pointer;
      }

      .item-img {
        margin-top: 12px;
        height: 160px;
      }

      .fit-content {
        width: fit-content;
      }

      .item-img-list {
        .img-list-item {
          margin-top: 8px;
        }
        .item-img-box {
          margin-right: 12px;
          position: relative;
          width: 133px;
          height: 100px;

          .item-img-dlete {
            position: absolute;
            padding: 8px;
            font-size: 32px;
            top: -16px;
            right: -16px;
            color: red;
            cursor: pointer;
          }
        }

        .small-img {
          margin: 0;
          height: 100px;
          object-fit: cover;
        }
      }
    }
  }
}
</style>