import type { VNode } from 'vue';

export interface ComponentProps {
  /** 控制组件尺寸 */
  size?: 'small' | 'medium' | 'large'
  /** 是否禁用状态 */
  disabled?: boolean
  /** 双向绑定的值 */
  modelValue: string | number
}

export interface ComponentEmits {
  (e: 'update:modelValue', value: string | number): void
  (e: 'change', value: string | number): void
  (e: 'focus', event: FocusEvent): void
}

export type ComponentSlots = {
  /** 默认插槽内容 */
  default?: (props: { value: string | number }) => VNode[]
  /** 头部内容 */
  header?: (props: { title: string }) => VNode[]
}