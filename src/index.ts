import type { App, Plugin } from 'vue'
import { ArticleList, HeroSlide } from './components'

// 全部引入插件
const OfficialBlock = {
  install: (app: App) => {
    app.component('ArticleList', ArticleList)
    app.component('HeroSlide', HeroSlide)
  },
} satisfies Plugin

// 默认导出插件
export default OfficialBlock

// 按需引入 - 导出组件
export { ArticleList, HeroSlide }

// 按需引入 - 导出单个组件的插件
export { ArticleListPlugin, HeroSlidePlugin } from './components'

// 导出类型定义
export type { ComponentProps, ComponentEmits, ComponentSlots } from './components'