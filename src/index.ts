import type { App, Plugin } from 'vue'
import ArticleList from './components/ArticleList'
import HeroSlide from './components/HeroSlide'

// 全部引入插件
export default {
  install: (app: App) => {
    app.component('ArticleList', ArticleList)
    app.component('HeroSlide', HeroSlide)
  },
} satisfies Plugin

// 按需引入 - 导出组件
export { ArticleList }
export { HeroSlide }

// 按需引入 - 导出单个组件的插件
export { default as ArticleListPlugin } from './components/ArticleList'
export { default as HeroSlidePlugin } from './components/HeroSlide'

// 导出类型定义
export type { ComponentProps, ComponentEmits, ComponentSlots } from './components/ArticleList/type'