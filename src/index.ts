import type { App, Plugin } from 'vue'
import { ArticleList, HeroSlide, RichTextEditor, ThemePreview } from './components'

// 全部引入插件
const OfficialBlock = {
  install: (app: App) => {
    app.component('ArticleList', ArticleList)
    app.component('HeroSlide', HeroSlide)
    app.component('RichTextEditor', RichTextEditor)
    app.component('ThemePreview', ThemePreview)
  },
} satisfies Plugin

// 默认导出插件
export default OfficialBlock

// 按需引入 - 导出组件
export { ArticleList, HeroSlide, RichTextEditor, ThemePreview }

// 按需引入 - 导出单个组件的插件
export { ArticleListPlugin, HeroSlidePlugin } from './components'

// 导出主题相关功能
export { useTheme } from './composables/useTheme'
export { ThemeUtils, ResponsiveUtils, AnimationUtils, StorageUtils } from './utils/theme'

// 导出类型定义
export type {
  ComponentProps,
  ComponentEmits,
  ComponentSlots,
  HeroSlideProps,
  HeroSlideEmits,
  SlideItem,
  IDomEditor,
  IEditorConfig,
  IToolbarConfig
} from './components'

export type { ThemeConfig } from './composables/useTheme'