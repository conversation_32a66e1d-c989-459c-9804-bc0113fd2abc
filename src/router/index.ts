import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/guide/introduction'
  },
  {
    path: '/guide',
    name: 'Guide',
    component: () => import('../views/Layout.vue'),
    children: [
      {
        path: 'introduction',
        name: 'Introduction',
        component: () => import('../views/guide/Introduction.vue'),
        meta: { title: '介绍' }
      },
      {
        path: 'installation',
        name: 'Installation',
        component: () => import('../views/guide/Installation.vue'),
        meta: { title: '安装' }
      },
      {
        path: 'quickstart',
        name: 'QuickStart',
        component: () => import('../views/guide/QuickStart.vue'),
        meta: { title: '快速开始' }
      }
    ]
  },
  {
    path: '/components',
    name: 'Components',
    component: () => import('../views/Layout.vue'),
    children: [
      {
        path: 'article-list',
        name: 'ArticleListDemo',
        component: () => import('../views/components/ArticleListDemo.vue'),
        meta: { title: 'ArticleList 文章列表' }
      },
      {
        path: 'hero-slide',
        name: 'HeroSlideDemo',
        component: () => import('../views/components/HeroSlideDemo.vue'),
        meta: { title: 'HeroSlide 轮播图' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router
