import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/guide/introduction'
  },
  {
    path: '/guide',
    name: 'Guide',
    component: () => import('../views/Layout.vue'),
    children: [
      {
        path: 'introduction',
        name: 'Introduction',
        component: () => import('../views/guide/Introduction.vue'),
        meta: { title: '介绍' }
      },
      {
        path: 'installation',
        name: 'Installation',
        component: () => import('../views/guide/Installation.vue'),
        meta: { title: '安装' }
      },
      {
        path: 'quickstart',
        name: 'QuickStart',
        component: () => import('../views/guide/QuickStart.vue'),
        meta: { title: '快速开始' }
      }
    ]
  },
  {
    path: '/components',
    name: 'Components',
    component: () => import('../views/Layout.vue'),
    children: [
      {
        path: 'article-list',
        name: 'ArticleListDemo',
        component: () => import('../views/components/ArticleListDemo.vue'),
        meta: { title: 'ArticleList 文章列表' }
      },
      {
        path: 'hero-slide',
        name: 'HeroSlideDemo',
        component: () => import('../views/components/HeroSlideDemo.vue'),
        meta: { title: 'HeroSlide 轮播图' }
      },
      {
        path: 'rich-text-editor',
        name: 'RichTextEditorDemo',
        component: () => import('../views/components/RichTextEditorDemo.vue'),
        meta: { title: 'RichTextEditor 富文本编辑器' }
      },
      {
        path: 'theme',
        name: 'ThemeDemo',
        component: () => import('../views/components/ThemeDemo.vue'),
        meta: { title: 'Theme 主题系统' }
      },
      {
        path: 'drag-sort',
        name: 'DragSortDemo',
        component: () => import('../views/components/DragSortDemo.vue'),
        meta: { title: '拖拽排序演示' }
      },
      {
        path: 'drag-limit',
        name: 'DragLimitDemo',
        component: () => import('../views/components/DragLimitDemo.vue'),
        meta: { title: '拖拽限制演示' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router
