/* 主题配置文件 */

/* 导入 Inter 字体 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap');

/* CSS 变量定义 - 主题颜色系统 */
:root {
  --button-bg-color: #0032a0;
  --button-text-color: #ffffff;
  --button-hover-bg-color: #0048e8;
  
  --text-primary-color: #1a1c20;
  --text-white-color: #ffffff;
  --text-important-color: #0032a0;
  --text-secondary-color: #0048e8;
  --text-tertiary-color: rgba(26, 28, 32, 0.6);
  --text-disabled-color: #c0c0c4;

  --bg-primary-color: #ffffff;

  /* 主色调 - 现代蓝色系 */
  --theme-primary: #3b82f6;
  --theme-primary-50: #eff6ff;
  --theme-primary-100: #dbeafe;
  --theme-primary-200: #bfdbfe;
  --theme-primary-300: #93c5fd;
  --theme-primary-400: #60a5fa;
  --theme-primary-500: #3b82f6;
  --theme-primary-600: #2563eb;
  --theme-primary-700: #1d4ed8;
  --theme-primary-800: #1e40af;
  --theme-primary-900: #1e3a8a;
  --theme-primary-950: #172554;

  /* 辅助色 - 现代紫色系 */
  --theme-secondary: #a855f7;
  --theme-secondary-50: #faf5ff;
  --theme-secondary-100: #f3e8ff;
  --theme-secondary-200: #e9d5ff;
  --theme-secondary-300: #d8b4fe;
  --theme-secondary-400: #c084fc;
  --theme-secondary-500: #a855f7;
  --theme-secondary-600: #9333ea;
  --theme-secondary-700: #7c3aed;
  --theme-secondary-800: #6b21a8;
  --theme-secondary-900: #581c87;
  --theme-secondary-950: #3b0764;

  /* 功能色 */
  --theme-success: #22c55e;
  --theme-warning: #f59e0b;
  --theme-error: #ef4444;
  --theme-info: #3b82f6;

  /* 中性色 - 现代灰色系 */
  --theme-gray-50: #f9fafb;
  --theme-gray-100: #f3f4f6;
  --theme-gray-200: #e5e7eb;
  --theme-gray-300: #d1d5db;
  --theme-gray-400: #9ca3af;
  --theme-gray-500: #6b7280;
  --theme-gray-600: #4b5563;
  --theme-gray-700: #374151;
  --theme-gray-800: #1f2937;
  --theme-gray-900: #111827;
  --theme-gray-950: #030712;

  /* 字体配置 */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* 字号配置 - 默认字号为 14px */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px - 默认字号 */
  --font-size-default: 0.875rem; /* 14px - 默认字号 */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */

  /* 行高配置 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距配置 */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */

  /* 圆角配置 */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */

  /* 阴影配置 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;

  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --theme-gray-50: #030712;
    --theme-gray-100: #111827;
    --theme-gray-200: #1f2937;
    --theme-gray-300: #374151;
    --theme-gray-400: #4b5563;
    --theme-gray-500: #6b7280;
    --theme-gray-600: #9ca3af;
    --theme-gray-700: #d1d5db;
    --theme-gray-800: #e5e7eb;
    --theme-gray-900: #f3f4f6;
    --theme-gray-950: #f9fafb;
  }
}

/* 全局基础样式 */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
  color: var(--theme-gray-900);
  background-color: var(--theme-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--theme-gray-900);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

/* 段落样式 */
p {
  margin: 0;
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
  color: var(--theme-gray-700);
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--theme-primary-600);
  text-decoration: underline;
}

/* 代码样式 */
code, pre {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
}

code {
  background-color: var(--theme-gray-100);
  color: var(--theme-gray-800);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
}

pre {
  background-color: var(--theme-gray-100);
  color: var(--theme-gray-800);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  line-height: var(--line-height-relaxed);
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--theme-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-gray-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-gray-400);
}

/* 选择文本样式 */
::selection {
  background-color: var(--theme-primary-200);
  color: var(--theme-primary-900);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 工具类 */
.text-theme-primary { color: var(--theme-primary) !important; }
.bg-theme-primary { background-color: var(--theme-primary) !important; }
.border-theme-primary { border-color: var(--theme-primary) !important; }

.text-default { 
  font-size: var(--font-size-default) !important; 
  line-height: var(--line-height-normal) !important; 
}

.font-sans { font-family: var(--font-family-sans) !important; }
.font-mono { font-family: var(--font-family-mono) !important; }

/* 响应式工具类 */
.container-responsive {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 var(--spacing-xl);
  }
}

/* 动画工具类 */
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
