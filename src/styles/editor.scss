.editor-content {
  margin-top: 16px;
  font-size: 16px;
  line-height: 1.75;
  color: $text-primary;

  b, strong {
    font-weight: 500;
    color: $text-important;
  }

  h1, h1 * {
    font-size: 40px;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: .02em;
  }

  h2, h2 * {
    font-size: 36px;
    line-height: 1.33;
    font-weight: 500;
  }

  h3, h3 * {
    font-size: 26px;
    font-weight: 700;
    line-height: 1.69;
    letter-spacing: .1em;
  }

  h4, h4 * {
    font-size: 24px;
    font-weight: 500;
    line-height: 1.5;
  }

  h5, h5 * {
    font-size: 20px;
    font-weight: 700;
    line-height: 1.6;
    letter-spacing: .1em;
  }

  a {
    color: #0048e8;
    text-decoration: none;
    background-image: linear-gradient(90deg,#0048e8,#0048e8);
    background-size: 0 1px,100% 1px;
    background-position: 0 100%,100% 100%;
    background-repeat: no-repeat;
    transition: background-size .3s;
    padding-bottom: 2px;
  }

  a:focus, a:hover {
    background-size: 100% 1px,0 1px;
  }

  ol:not(:last-child),ul:not(:last-child) {
    margin-bottom: 16px;
  }

  ol:not(:first-child),ul:not(:first-child) {
    margin-top: 16px;
  }

  ol>li,ul>li {
    position: relative;
    padding-left: 1.6em;
  }

  ul>li:before {
    content: "";
    position: absolute;
    top: .7em;
    left: .5em;
    width: 5px;
    height: 5px;
    background: #000;
    border-radius: 50%;
  }

  ol {
    counter-reset: section;
    list-style-type: none;
  }

  ol>li {
    list-style-type: none;
    position: relative;
  }

  ol>li:before {
    counter-increment: section;
    content: counters(section,".") ".";
    position: absolute;
    top: 0;
    left: 0;
    background: none;
  }

  img {
    width: 100%;
  }

  em {
    font-style: italic;
  }

  table.scrollable {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-color: $primary-color #e4e4e6;
    scrollbar-width: thin;
  }

  table.scrollable::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e4e4e6;
  }

  table.scrollable::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background-color: $primary-color;
  }

  table.scrollable::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $primary-color;
  }

  table:not(.table-striped) tr {
    background-color: #fff;
  }

  table:not(.table-striped) tr td {
    padding: 8px 32px 8px 16px;
    vertical-align: middle;
    line-height: 1.25;
  }

  table:not(.table-striped) tr td:first-child:not(:only-child) {
    background-color: #f2f8ff;
  }

  table:not(.table-striped) tr th {
    background-color: $primary-color;
    background-color: var(--text-color--secondary,$primary-color);
    color: #fff;
    padding: 8px 32px 8px 16px;
    vertical-align: middle;
  }

  table:not(.table-striped) tr th *,table:not(.table-striped) tr th b,table:not(.table-striped) tr th em,table:not(.table-striped) tr th p,table:not(.table-striped) tr th strong {
    color: #fff;
  }

  table:not(.table-striped) tr th p {
    font-size: 18px;
    font-weight: 500;
    line-height: 1.33;
  }

  table.table-striped td {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.75;
    padding: 13.5px 36px;
    color: #1a1c20;
  }

  .column-1 {
    -moz-column-count: 1;
    column-count: 1;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .column-1>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }

  .column-2 {
    -moz-column-count: 2;
    column-count: 2;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .column-2>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }

  .column-3 {
    -moz-column-count: 3;
    column-count: 3;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .column-3>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }

  .column-4 {
    -moz-column-count: 4;
    column-count: 4;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .column-4>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }

  table.table-striped tr:nth-child(2n) {
    background-color: #f2f8ff;
  }

  table.table-striped tr:nth-child(odd) {
    background-color: #fff;
  }

  table.table-striped tr:first-child {
    background-color: $primary-color;
  }

  table.table-striped th {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.25;
    letter-spacing: .01em;
    padding: 8px 36px;
    color: #fff;
  }
}

@media(max-width: 1023.98px) {
  .editor-content {
    font-size:14px;
    line-height: 1.714;
    letter-spacing: .01em;
  }
}

@media(max-width: 1023.98px) {
  .editor-content h1,.editor-content h1 * {
    font-size:32px;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: .02em;
  }
}

@media(max-width: 767.98px) {
  .editor-content h1,.editor-content h1 * {
    font-size:24px;
    line-height: 1.09;
  }
}

@media(max-width: 1023.98px) {
  .editor-content h2,.editor-content h2 * {
    font-size:26px;
    line-height: 1.2;
    font-weight: 700;
  }
}

@media(max-width: 767.98px) {
  .editor-content h2,.editor-content h2 * {
    font-size: 22px;
    line-height: 1.2;
    font-weight: 500;
  }
}

@media(max-width: 1023.98px) {
  .editor-content h3,.editor-content h3 * {
    font-size:24px;
    line-height: 1;
  }
}

@media(max-width: 767.98px) {
  .editor-content h3,.editor-content h3 * {
    font-size:20px;
    line-height: 1.2;
    letter-spacing: .08em;
  }
}

@media(max-width: 767.98px) {
  .editor-content h4,.editor-content h4 * {
    font-size:20px;
    line-height: 1.2;
    letter-spacing: -.005em;
  }
}

@media(max-width: 1023.98px) {
  .editor-content h5,.editor-content h5 * {
    font-size:16px;
    font-weight: 500;
    line-height: 1.125;
    letter-spacing: .01em;
  }
}

@media(max-width: 767.98px) {
  .editor-content h5,.editor-content h5 * {
    font-weight:700;
    line-height: 1.25;
    letter-spacing: .06em;
  }
}

@media(max-width: 575.98px) {
  .editor-content table.scrollable-sm {
    display:block;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-color: $primary-color #e4e4e6;
    scrollbar-width: thin;
  }

  .editor-content table.scrollable-sm::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e4e4e6;
  }

  .editor-content table.scrollable-sm::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background-color: $primary-color;
  }

  .editor-content table.scrollable-sm::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $primary-color;
  }
}

@media(max-width: 767.98px) {
  .editor-content table.scrollable-md {
    display:block;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-color: $primary-color #e4e4e6;
    scrollbar-width: thin;
  }

  .editor-content table.scrollable-md::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e4e4e6;
  }

  .editor-content table.scrollable-md::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background-color: $primary-color;
  }

  .editor-content table.scrollable-md::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $primary-color;
  }
}

@media(max-width: 1023.98px) {
  .editor-content table.scrollable-lg {
    display:block;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-color: $primary-color #e4e4e6;
    scrollbar-width: thin;
  }

  .editor-content table.scrollable-lg::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e4e4e6;
  }

  .editor-content table.scrollable-lg::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background-color: $primary-color;
  }

  .editor-content table.scrollable-lg::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $primary-color;
  }
}

@media(max-width: 1279.98px) {
  .editor-content table.scrollable-xl {
    display:block;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-color: $primary-color #e4e4e6;
    scrollbar-width: thin;
  }

  .editor-content table.scrollable-xl::-webkit-scrollbar-track {
    border-radius: 8px;
    background-color: #e4e4e6;
  }

  .editor-content table.scrollable-xl::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    border-radius: 8px;
    background-color: $primary-color;
  }

  .editor-content table.scrollable-xl::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: $primary-color;
  }
}

@media(max-width: 767.98px) {
  .editor-content table:not(.table-striped) tr td {
    padding:10px 8px;
  }
}

@media(max-width: 767.98px) {
  .editor-content table:not(.table-striped) tr th {
    height:34px;
    padding: 8px 12px;
  }
}

@media(max-width: 1023.98px) {
  .editor-content table:not(.table-striped) tr th p {
    font-size:16px;
    line-height: 1.25;
    letter-spacing: .01em;
  }
}

@media(max-width: 767.98px) {
  .editor-content table:not(.table-striped) tr th p {
    line-height:1.5;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.25;
    letter-spacing: .01em;
  }
}

@media(max-width: 767.98px)and (max-width:1023.98px) {
  .editor-content table:not(.table-striped) tr th p {
    font-size:14px;
    line-height: 1.285;
    letter-spacing: .01em;
  }
}

@media(max-width: 1023.98px) {
  .editor-content table.table-striped th {
    font-size:14px;
    line-height: 1.285;
    letter-spacing: .01em;
    padding: 8px 16px;
  }
}

@media(max-width: 767.98px) {
  .editor-content table.table-striped th {
    padding:8px;
  }
}

@media(max-width: 1023.98px) {
  .editor-content table.table-striped td {
    font-size:14px;
    line-height: 1.714;
    letter-spacing: .01em;
    padding: 12px 16px;
  }
}

@media(max-width: 767.98px) {
  .editor-content table.table-striped td {
    padding:10px 8px;
  }
}

@media(min-width: 768px) {
  .editor-content .column-tablet-1 {
    -moz-column-count:1;
    column-count: 1;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-tablet-1>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 768px) {
  .editor-content .column-tablet-2 {
    -moz-column-count:2;
    column-count: 2;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-tablet-2>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 768px) {
  .editor-content .column-tablet-3 {
    -moz-column-count:3;
    column-count: 3;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-tablet-3>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 768px) {
  .editor-content .column-tablet-4 {
    -moz-column-count:4;
    column-count: 4;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-tablet-4>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 1280px) {
  .editor-content .column-desktop-1 {
    -moz-column-count:1;
    column-count: 1;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-desktop-1>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 1280px) {
  .editor-content .column-desktop-2 {
    -moz-column-count:2;
    column-count: 2;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-desktop-2>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 1280px) {
  .editor-content .column-desktop-3 {
    -moz-column-count:3;
    column-count: 3;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-desktop-3>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(min-width: 1280px) {
  .editor-content .column-desktop-4 {
    -moz-column-count:4;
    column-count: 4;
    grid-column-gap: 80px;
    -moz-column-gap: 80px;
    column-gap: 80px;
  }

  .editor-content .column-desktop-4>* {
    -moz-column-break-inside: avoid;
    break-inside: avoid-column;
  }
}

@media(max-width: 1023.98px) {
  .container-content,.container-content-952 {
    padding-left:24px;
    padding-right: 24px;
    max-width: calc(var(--max-width) + 48px);
  }
}

@media(max-width: 767.98px) {
  .container-content,.container-content-952 {
    padding-left:12px;
    padding-right: 12px;
    max-width: calc(var(--max-width) + 24px);
  }
}

@media(max-width: 575.98px) {
  .container-content,.container-content-952 {
    padding-left:12px;
    padding-right: 12px;
    max-width: calc(var(--max-width) + 24px);
  }
}