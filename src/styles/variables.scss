// Sass 变量文件 - 整合主题系统
// 这个文件会自动导入到所有 Sass/SCSS 文件中

// 导入 Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700;800&display=swap');

// ===== 主色调系统 =====
// 现代蓝色系 - 主色调
$primary-color: #0032a0;
$primary-50: #eff6ff;
$primary-100: #dbeafe;
$primary-200: #bfdbfe;
$primary-300: #93c5fd;
$primary-400: #60a5fa;
$primary-500: #3b82f6;
$primary-600: #0048e8;
$primary-700: #1d4ed8;
$primary-800: #1e40af;
$primary-900: #1e3a8a;
$primary-950: #172554;

// 兼容旧版本的变量名
$primary-hover-color: $primary-600;

// 现代紫色系 - 辅助色
$secondary-color: #a855f7;
$secondary-50: #faf5ff;
$secondary-100: #f3e8ff;
$secondary-200: #e9d5ff;
$secondary-300: #d8b4fe;
$secondary-400: #c084fc;
$secondary-500: #a855f7;
$secondary-600: #9333ea;
$secondary-700: #7c3aed;
$secondary-800: #6b21a8;
$secondary-900: #581c87;
$secondary-950: #3b0764;

// 功能色系
$success-color: #22c55e;
$warning-color: #f59e0b;
$error-color: #ef4444;
$info-color: #3b82f6;

// ===== 中性色系统 =====
// 现代灰色系
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #1a1c20;
$gray-950: #030712;

// ===== 文本颜色系统 =====
$text-primary: $gray-900;
$text-white: #ffffff;
$text-important: $primary-color;
$text-secondary: $primary-600;
$text-tertiary: rgba($gray-900, 0.6);
$text-disabled: #c0c0c4;
$text-muted: $gray-600;

// ===== 背景颜色系统 =====
$bg-primary: #ffffff;
$bg-secondary: $gray-50;
$bg-tertiary: $gray-100;

// ===== 按钮颜色系统 =====
$button-bg: $primary-color;
$button-text: #ffffff;
$button-hover-bg: $primary-600;

// ===== 字体系统 =====
// 字体族
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

// 字体大小 - 默认字号为 14px
$font-size-xs: 0.75rem;     // 12px
$font-size-sm: 0.875rem;    // 14px - 默认字号
$font-size-default: 0.875rem; // 14px - 默认字号
$font-size-base: 1rem;      // 16px
$font-size-lg: 1.125rem;    // 18px
$font-size-xl: 1.25rem;     // 20px
$font-size-2xl: 1.5rem;     // 24px
$font-size-3xl: 1.875rem;   // 30px
$font-size-4xl: 2.25rem;    // 36px

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// ===== 间距系统 =====
$spacing-xs: 0.25rem;   // 4px
$spacing-sm: 0.5rem;    // 8px
$spacing-md: 1rem;      // 16px
$spacing-lg: 1.5rem;    // 24px
$spacing-xl: 2rem;      // 32px
$spacing-2xl: 3rem;     // 48px
$spacing-3xl: 4rem;     // 64px

// ===== 圆角系统 =====
$radius-none: 0;
$radius-sm: 0.25rem;    // 4px
$radius-md: 0.375rem;   // 6px
$radius-lg: 0.5rem;     // 8px
$radius-xl: 0.75rem;    // 12px
$radius-2xl: 1rem;      // 16px
$radius-full: 9999px;

// ===== 阴影系统 =====
$shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
$shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
$shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
$shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
$shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

// ===== 过渡动画系统 =====
$transition-fast: 150ms ease-in-out;
$transition-normal: 200ms ease-in-out;
$transition-slow: 300ms ease-in-out;

// ===== 响应式断点系统 =====
$breakpoint-xs: 475px;
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// ===== Z-index 层级系统 =====
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;
$z-toast: 1080;

// 混合器 (Mixins)
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 响应式混合器
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin mobile-up {
  @media (min-width: #{$breakpoint-sm}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

// 按钮混合器
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: 1px solid transparent;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-fast;
  user-select: none;

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $button-bg;
  color: $button-text;

  &:hover:not(:disabled) {
    background-color: $button-hover-bg;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: $secondary-color;
  color: white;

  &:hover:not(:disabled) {
    background-color: darken($secondary-color, 10%);
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($secondary-color, 0.3);
  }
}

@mixin button-outline {
  @include button-base;
  background-color: transparent;
  color: $primary-color;
  border-color: $primary-color;

  &:hover:not(:disabled) {
    background-color: $primary-color;
    color: white;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

// 卡片混合器
@mixin card {
  background-color: white;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
  transition: box-shadow $transition-fast;

  &:hover {
    box-shadow: $shadow-md;
  }
}

// 输入框混合器
@mixin input {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $gray-300;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  line-height: $line-height-normal;
  background-color: white;
  transition: border-color $transition-fast, box-shadow $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }

  &:disabled {
    background-color: $gray-100;
    color: $text-disabled;
    cursor: not-allowed;
  }

  &::placeholder {
    color: $gray-400;
  }
}

// ===== CSS 变量定义 =====
// 将 Sass 变量转换为 CSS 变量，用于主题系统
:root {
  // 主色调
  --theme-primary: #{$primary-color};
  --theme-primary-50: #{$primary-50};
  --theme-primary-100: #{$primary-100};
  --theme-primary-200: #{$primary-200};
  --theme-primary-300: #{$primary-300};
  --theme-primary-400: #{$primary-400};
  --theme-primary-500: #{$primary-500};
  --theme-primary-600: #{$primary-600};
  --theme-primary-700: #{$primary-700};
  --theme-primary-800: #{$primary-800};
  --theme-primary-900: #{$primary-900};
  --theme-primary-950: #{$primary-950};

  // 辅助色
  --theme-secondary: #{$secondary-color};
  --theme-secondary-50: #{$secondary-50};
  --theme-secondary-100: #{$secondary-100};
  --theme-secondary-200: #{$secondary-200};
  --theme-secondary-300: #{$secondary-300};
  --theme-secondary-400: #{$secondary-400};
  --theme-secondary-500: #{$secondary-500};
  --theme-secondary-600: #{$secondary-600};
  --theme-secondary-700: #{$secondary-700};
  --theme-secondary-800: #{$secondary-800};
  --theme-secondary-900: #{$secondary-900};
  --theme-secondary-950: #{$secondary-950};

  // 功能色
  --theme-success: #{$success-color};
  --theme-warning: #{$warning-color};
  --theme-error: #{$error-color};
  --theme-info: #{$info-color};

  // 中性色
  --theme-gray-50: #{$gray-50};
  --theme-gray-100: #{$gray-100};
  --theme-gray-200: #{$gray-200};
  --theme-gray-300: #{$gray-300};
  --theme-gray-400: #{$gray-400};
  --theme-gray-500: #{$gray-500};
  --theme-gray-600: #{$gray-600};
  --theme-gray-700: #{$gray-700};
  --theme-gray-800: #{$gray-800};
  --theme-gray-900: #{$gray-900};
  --theme-gray-950: #{$gray-950};

  // 按钮颜色
  --button-bg-color: #{$button-bg};
  --button-text-color: #{$button-text};
  --button-hover-bg-color: #{$button-hover-bg};

  // 文本颜色
  --text-primary-color: #{$text-primary};
  --text-white-color: #{$text-white};
  --text-important-color: #{$text-important};
  --text-secondary-color: #{$text-secondary};
  --text-tertiary-color: #{$text-tertiary};
  --text-disabled-color: #{$text-disabled};

  // 背景颜色
  --bg-primary-color: #{$bg-primary};

  // 字体配置
  --font-family-sans: #{$font-family-sans};
  --font-family-mono: #{$font-family-mono};

  // 字号配置
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-default: #{$font-size-default};
  --font-size-base: #{$font-size-base};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-2xl: #{$font-size-2xl};
  --font-size-3xl: #{$font-size-3xl};
  --font-size-4xl: #{$font-size-4xl};

  // 行高配置
  --line-height-tight: #{$line-height-tight};
  --line-height-normal: #{$line-height-normal};
  --line-height-relaxed: #{$line-height-relaxed};

  // 间距配置
  --spacing-xs: #{$spacing-xs};
  --spacing-sm: #{$spacing-sm};
  --spacing-md: #{$spacing-md};
  --spacing-lg: #{$spacing-lg};
  --spacing-xl: #{$spacing-xl};
  --spacing-2xl: #{$spacing-2xl};

  // 圆角配置
  --radius-sm: #{$radius-sm};
  --radius-md: #{$radius-md};
  --radius-lg: #{$radius-lg};
  --radius-xl: #{$radius-xl};
  --radius-2xl: #{$radius-2xl};

  // 阴影配置
  --shadow-sm: #{$shadow-sm};
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};
  --shadow-xl: #{$shadow-xl};

  // 过渡动画
  --transition-fast: #{$transition-fast};
  --transition-normal: #{$transition-normal};
  --transition-slow: #{$transition-slow};

  // Z-index 层级
  --z-dropdown: #{$z-dropdown};
  --z-sticky: #{$z-sticky};
  --z-fixed: #{$z-fixed};
  --z-modal-backdrop: #{$z-modal-backdrop};
  --z-modal: #{$z-modal};
  --z-popover: #{$z-popover};
  --z-tooltip: #{$z-tooltip};
  --z-toast: #{$z-toast};
}

// ===== 暗色主题支持 =====
@media (prefers-color-scheme: dark) {
  :root {
    --theme-gray-50: #{$gray-950};
    --theme-gray-100: #{$gray-900};
    --theme-gray-200: #{$gray-800};
    --theme-gray-300: #{$gray-700};
    --theme-gray-400: #{$gray-600};
    --theme-gray-500: #{$gray-500};
    --theme-gray-600: #{$gray-400};
    --theme-gray-700: #{$gray-300};
    --theme-gray-800: #{$gray-200};
    --theme-gray-900: #{$gray-100};
    --theme-gray-950: #{$gray-50};
  }
}

// ===== 全局基础样式 =====
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
  color: var(--theme-gray-900);
  background-color: var(--theme-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
}

// ===== 标题样式 =====
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: $font-weight-semibold;
  line-height: var(--line-height-tight);
  color: var(--theme-gray-900);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-sm); }

// ===== 段落样式 =====
p {
  margin: 0;
  font-size: var(--font-size-default);
  line-height: var(--line-height-normal);
  color: var(--theme-gray-700);
}

// ===== 链接样式 =====
a {
  color: var(--theme-primary);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--theme-primary-600);
    text-decoration: underline;
  }
}

// ===== 代码样式 =====
code, pre {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
}

code {
  background-color: var(--theme-gray-100);
  color: var(--theme-gray-800);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
}

pre {
  background-color: var(--theme-gray-100);
  color: var(--theme-gray-800);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  line-height: var(--line-height-relaxed);

  code {
    background: none;
    padding: 0;
    border-radius: 0;
  }
}

// ===== 滚动条样式 =====
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--theme-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-gray-300);
  border-radius: var(--radius-sm);

  &:hover {
    background: var(--theme-gray-400);
  }
}

// ===== 选择文本样式 =====
::selection {
  background-color: var(--theme-primary-200);
  color: var(--theme-primary-900);
}

// ===== 焦点样式 =====
:focus-visible {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

// ===== 工具类 =====
.text-theme-primary { color: var(--theme-primary) !important; }
.bg-theme-primary { background-color: var(--theme-primary) !important; }
.border-theme-primary { border-color: var(--theme-primary) !important; }

.text-default {
  font-size: var(--font-size-default) !important;
  line-height: var(--line-height-normal) !important;
}

.text-muted {
  color: var(--theme-gray-600) !important;
}

.font-sans { font-family: var(--font-family-sans) !important; }
.font-mono { font-family: var(--font-family-mono) !important; }

// ===== 响应式工具类 =====
.container-responsive {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);

  @include mobile-up {
    padding: 0 var(--spacing-lg);
  }

  @include desktop-up {
    padding: 0 var(--spacing-xl);
  }
}

// ===== 动画工具类 =====
.transition-fast { transition: all var(--transition-fast); }
.transition-normal { transition: all var(--transition-normal); }
.transition-slow { transition: all var(--transition-slow); }

// ===== 阴影工具类 =====
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
