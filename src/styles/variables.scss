// Sass 变量文件
// 这个文件会自动导入到所有 Sass/SCSS 文件中

// 颜色变量
$primary-color: #0032a0;
$primary-hover-color: #0048e8;
$secondary-color: #a855f7;
$success-color: #10b981;
$warning-color: #f59e0b;
$error-color: #ef4444;

// 文本颜色
$text-primary: #1a1c20;
$text-white: #ffffff;
$text-important: #0032a0;
$text-secondary: #0048e8;
$text-tertiary: rgba(26, 28, 32, 0.6);
$text-disabled: #c0c0c4;

// 背景颜色
$bg-primary: #ffffff;
$bg-secondary: #f8fafc;
$bg-tertiary: #f1f5f9;

// 按钮颜色
$button-bg: #0032a0;
$button-text: #ffffff;
$button-hover-bg: #0048e8;

// 灰色系列
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$gray-950: #030712;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// 字体族
$font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体权重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// 圆角
$radius-none: 0;
$radius-sm: 4px;
$radius-md: 6px;
$radius-lg: 8px;
$radius-xl: 12px;
$radius-2xl: 16px;
$radius-full: 9999px;

// 阴影
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// 过渡动画
$transition-fast: 150ms ease-in-out;
$transition-normal: 300ms ease-in-out;
$transition-slow: 500ms ease-in-out;

// 断点
$breakpoint-xs: 475px;
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// Z-index 层级
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// 混合器 (Mixins)
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 响应式混合器
@mixin mobile {
  @media (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin mobile-up {
  @media (min-width: #{$breakpoint-sm}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

// 按钮混合器
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: 1px solid transparent;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  line-height: $line-height-tight;
  text-decoration: none;
  cursor: pointer;
  transition: all $transition-fast;
  user-select: none;

  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin button-primary {
  @include button-base;
  background-color: $button-bg;
  color: $button-text;

  &:hover:not(:disabled) {
    background-color: $button-hover-bg;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: $secondary-color;
  color: white;

  &:hover:not(:disabled) {
    background-color: darken($secondary-color, 10%);
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($secondary-color, 0.3);
  }
}

@mixin button-outline {
  @include button-base;
  background-color: transparent;
  color: $primary-color;
  border-color: $primary-color;

  &:hover:not(:disabled) {
    background-color: $primary-color;
    color: white;
  }

  &:focus {
    box-shadow: 0 0 0 3px rgba($primary-color, 0.3);
  }
}

// 卡片混合器
@mixin card {
  background-color: white;
  border-radius: $radius-lg;
  box-shadow: $shadow-sm;
  padding: $spacing-lg;
  transition: box-shadow $transition-fast;

  &:hover {
    box-shadow: $shadow-md;
  }
}

// 输入框混合器
@mixin input {
  width: 100%;
  padding: $spacing-sm $spacing-md;
  border: 1px solid $gray-300;
  border-radius: $radius-md;
  font-size: $font-size-sm;
  line-height: $line-height-normal;
  background-color: white;
  transition: border-color $transition-fast, box-shadow $transition-fast;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }

  &:disabled {
    background-color: $gray-100;
    color: $text-disabled;
    cursor: not-allowed;
  }

  &::placeholder {
    color: $gray-400;
  }
}
