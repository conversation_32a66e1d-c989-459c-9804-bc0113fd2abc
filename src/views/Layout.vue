<template>
  <a-layout class="layout">
    <!-- 顶部导航栏 -->
    <a-layout-header class="header">
      <div class="header-content">
        <div class="logo">
          <h1>OfficialBlock</h1>
          <a-tag color="blue" size="small">v{{ version }}</a-tag>
        </div>
        <nav class="nav">
          <router-link to="/guide/introduction" class="nav-link">指南</router-link>
          <router-link to="/components/article-list" class="nav-link">组件</router-link>
          <a-button
            type="text"
            class="mobile-menu-btn"
            @click="toggleMobileMenu"
            :icon="isMobileMenuOpen ? 'icon-close' : 'icon-menu'"
          >
            <template #icon>
              <icon-menu v-if="!isMobileMenuOpen" />
              <icon-close v-if="isMobileMenuOpen" />
            </template>
          </a-button>
        </nav>
      </div>
    </a-layout-header>

    <a-layout class="main-container">
      <!-- 移动端遮罩层 -->
      <div
        v-if="isMobileMenuOpen"
        class="mobile-overlay"
        @click="toggleMobileMenu"
      ></div>

      <!-- 左侧菜单栏 -->
      <a-layout-sider
        class="sidebar"
        :class="{ 'mobile-open': isMobileMenuOpen }"
        :width="280"
        :collapsed="false"
        :collapsible="false"
      >
        <a-menu
          :selected-keys="selectedKeys"
          :default-open-keys="['guide', 'components']"
          :style="{ height: '100%', borderRight: 0 }"
          @menu-item-click="handleMenuClick"
        >
          <a-sub-menu key="guide" title="开发指南">
            <template #icon>
              <icon-book />
            </template>
            <a-menu-item key="/guide/introduction">介绍</a-menu-item>
            <a-menu-item key="/guide/installation">安装</a-menu-item>
            <a-menu-item key="/guide/quickstart">快速开始</a-menu-item>
          </a-sub-menu>

          <a-sub-menu key="components" title="组件">
            <template #icon>
              <icon-apps />
            </template>
            <a-menu-item key="/components/article-list">ArticleList 文章列表</a-menu-item>
            <a-menu-item key="/components/hero-slide">HeroSlide 轮播图</a-menu-item>
            <a-menu-item key="/components/rich-text-editor">RichTextEditor 富文本编辑器</a-menu-item>
            <a-menu-item key="/components/theme">Theme 主题系统</a-menu-item>
          </a-sub-menu>
        </a-menu>
      </a-layout-sider>

      <!-- 主内容区域 -->
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  IconMenu,
  IconClose,
  IconBook,
  IconApps
} from '@arco-design/web-vue/es/icon'

const route = useRoute()
const router = useRouter()

const version = ref('1.0.1')
const isMobileMenuOpen = ref(false)
const selectedKeys = ref([route.path])

// 监听路由变化，更新选中的菜单项
watch(() => route.path, (newPath) => {
  selectedKeys.value = [newPath]
}, { immediate: true })

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const handleMenuClick = (key: string) => {
  router.push(key)
  // 在移动端点击菜单项后关闭菜单
  if (window.innerWidth <= 768) {
    isMobileMenuOpen.value = false
  }
}
</script>

<style scoped>
.layout {
  min-height: 100vh;
}

.header {
  background: #fff;
  border-bottom: 1px solid var(--color-border-2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 60px;
  padding: 0;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: rgb(var(--primary-6));
}

.nav {
  display: flex;
  gap: 32px;
  align-items: center;
}

.nav-link {
  color: var(--color-text-2);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: rgb(var(--primary-6));
}

.mobile-menu-btn {
  display: none;
}

.main-container {
  padding-top: 60px;
  min-height: calc(100vh - 60px);
}

.sidebar {
  background: #fff;
  border-right: 1px solid var(--color-border-2);
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
  overflow-y: auto;
  z-index: 999;
}

.content {
  margin-left: 280px;
  padding: 32px;
  background: #f3f4f6;
  min-height: calc(100vh - 60px);
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: inline-flex;
  }

  .nav-link {
    display: none;
  }

  .mobile-overlay {
    display: block;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s;
    z-index: 1001;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
    padding: 16px;
  }

  .header-content {
    padding: 0 16px;
  }

  .nav {
    gap: 16px;
  }
}
</style>
