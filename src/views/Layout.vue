<template>
  <div class="layout">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <div class="logo">
          <h1>OfficialBlock</h1>
          <span class="version">v{{ version }}</span>
        </div>
        <nav class="nav">
          <router-link to="/guide/introduction" class="nav-link">指南</router-link>
          <router-link to="/components/article-list" class="nav-link">组件</router-link>
          <button class="mobile-menu-btn" @click="toggleMobileMenu">
            <span></span>
            <span></span>
            <span></span>
          </button>
        </nav>
      </div>
    </header>

    <div class="main-container">
      <!-- 左侧菜单栏 -->
      <aside class="sidebar" :class="{ 'mobile-open': isMobileMenuOpen }">
        <div class="menu-section">
          <h3 class="menu-title">开发指南</h3>
          <ul class="menu-list">
            <li>
              <router-link to="/guide/introduction" class="menu-item">
                介绍
              </router-link>
            </li>
            <li>
              <router-link to="/guide/installation" class="menu-item">
                安装
              </router-link>
            </li>
            <li>
              <router-link to="/guide/quickstart" class="menu-item">
                快速开始
              </router-link>
            </li>
          </ul>
        </div>

        <div class="menu-section">
          <h3 class="menu-title">组件</h3>
          <ul class="menu-list">
            <li>
              <router-link to="/components/article-list" class="menu-item">
                ArticleList 文章列表
              </router-link>
            </li>
            <li>
              <router-link to="/components/hero-slide" class="menu-item">
                HeroSlide 轮播图
              </router-link>
            </li>
          </ul>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const version = ref('1.0.1')
const isMobileMenuOpen = ref(false)

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}
</script>

<style scoped>
.layout {
  min-height: 100vh;
  background: #f8f9fa;
}

.header {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 60px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.version {
  background: #f0f9ff;
  color: #409eff;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.nav {
  display: flex;
  gap: 32px;
}

.nav-link {
  color: #606266;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #409eff;
}

.mobile-menu-btn {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.mobile-menu-btn span {
  width: 20px;
  height: 2px;
  background: #606266;
  transition: all 0.3s;
}

.main-container {
  display: flex;
  padding-top: 60px;
  min-height: calc(100vh - 60px);
}

.sidebar {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  padding: 24px 0;
  overflow-y: auto;
  position: fixed;
  left: 0;
  top: 60px;
  bottom: 0;
}

.menu-section {
  margin-bottom: 32px;
}

.menu-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 24px;
}

.menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.menu-item {
  display: block;
  padding: 8px 24px;
  color: #606266;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
}

.menu-item:hover {
  color: #409eff;
  background: #f0f9ff;
}

.menu-item.router-link-active {
  color: #409eff;
  background: #f0f9ff;
  border-left-color: #409eff;
  font-weight: 500;
}

.content {
  flex: 1;
  margin-left: 280px;
  padding: 32px;
  max-width: calc(100vw - 280px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .nav-link {
    display: none;
  }

  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s;
    z-index: 1001;
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .content {
    margin-left: 0;
    max-width: 100vw;
    padding: 16px;
  }

  .header-content {
    padding: 0 16px;
  }

  .nav {
    gap: 16px;
  }
}
</style>
