<template>
  <div class="page">
    <h1>安装</h1>
    
    <h2>环境要求</h2>
    <ul>
      <li>Node.js 16+ 或 18+</li>
      <li>Vue 3.0+</li>
    </ul>

    <h2>使用包管理器安装</h2>
    
    <h3>npm</h3>
    <div class="code-block">
      <pre><code>npm install officialblock</code></pre>
      <button class="copy-btn" @click="copyCode('npm install officialblock')">复制</button>
    </div>

    <h3>yarn</h3>
    <div class="code-block">
      <pre><code>yarn add officialblock</code></pre>
      <button class="copy-btn" @click="copyCode('yarn add officialblock')">复制</button>
    </div>

    <h3>pnpm</h3>
    <div class="code-block">
      <pre><code>pnpm add officialblock</code></pre>
      <button class="copy-btn" @click="copyCode('pnpm add officialblock')">复制</button>
    </div>

    <h2>CDN 引入</h2>
    <p>通过 CDN 的方式引入，适合快速原型开发。</p>
    
    <div class="code-block">
      <pre><code>&lt;!-- 引入样式 --&gt;
&lt;link rel="stylesheet" href="https://unpkg.com/officialblock/dist/style.css"&gt;

&lt;!-- 引入组件库 --&gt;
&lt;script src="https://unpkg.com/officialblock/dist/official-block.umd.js"&gt;&lt;/script&gt;</code></pre>
      <button class="copy-btn" @click="copyCode(cdnCode)">复制</button>
    </div>

    <div class="tip">
      <p><strong>提示：</strong>我们建议使用 CDN 引入 OfficialBlock 的用户在链接地址上锁定版本，以免将来 OfficialBlock 升级时受到非兼容性更新的影响。</p>
    </div>

    <h2>Hello World</h2>
    <p>通过 CDN 的方式我们可以很容易地使用 OfficialBlock 写出一个 Hello world 页面。</p>
    
    <div class="code-block">
      <pre><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;meta charset="UTF-8"&gt;
  &lt;title&gt;Hello OfficialBlock&lt;/title&gt;
  &lt;script src="https://unpkg.com/vue@3/dist/vue.global.js"&gt;&lt;/script&gt;
  &lt;link rel="stylesheet" href="https://unpkg.com/officialblock/dist/style.css"&gt;
  &lt;script src="https://unpkg.com/officialblock/dist/official-block.umd.js"&gt;&lt;/script&gt;
&lt;/head&gt;
&lt;body&gt;
  &lt;div id="app"&gt;
    &lt;article-list v-model="message"&gt;&lt;/article-list&gt;
  &lt;/div&gt;

  &lt;script&gt;
    const { createApp } = Vue
    const { OfficialBlock } = window.OfficialBlock

    createApp({
      data() {
        return {
          message: 'Hello OfficialBlock!'
        }
      }
    })
    .use(OfficialBlock.default)
    .mount('#app')
  &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
      <button class="copy-btn" @click="copyCode(helloWorldCode)">复制</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const cdnCode = `<!-- 引入样式 -->
<link rel="stylesheet" href="https://unpkg.com/officialblock/dist/style.css">

<!-- 引入组件库 -->
<script src="https://unpkg.com/officialblock/dist/official-block.umd.js"><\/script>`

const helloWorldCode = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Hello OfficialBlock</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"><\/script>
  <link rel="stylesheet" href="https://unpkg.com/officialblock/dist/style.css">
  <script src="https://unpkg.com/officialblock/dist/official-block.umd.js"><\/script>
</head>
<body>
  <div id="app">
    <article-list v-model="message"></article-list>
  </div>

  <script>
    const { createApp } = Vue
    const { OfficialBlock } = window.OfficialBlock

    createApp({
      data() {
        return {
          message: 'Hello OfficialBlock!'
        }
      }
    })
    .use(OfficialBlock.default)
    .mount('#app')
  <\/script>
</body>
</html>`

const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    // 这里可以添加复制成功的提示
    console.log('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}
</script>

<style scoped>
.page {
  max-width: 800px;
}

.code-block {
  position: relative;
  margin: 16px 0;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.code-block pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
  background: transparent;
}

.code-block code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  background: transparent;
}

.copy-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s;
}

.copy-btn:hover {
  background: #337ecc;
}

.tip {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
  padding: 16px;
  margin: 24px 0;
}

.tip p {
  margin: 0;
  color: #409eff;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 32px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

h3 {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px 0;
}

p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 16px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
