<template>
  <div class="page">
    <h1>介绍</h1>
    
    <p class="description">
      OfficialBlock 是一个基于 Vue 3 + TypeScript 的组件库，提供了一系列高质量的 UI 组件，
      帮助开发者快速构建现代化的 Web 应用程序。
    </p>

    <h2>特性</h2>
    <ul class="feature-list">
      <li>🚀 基于 Vue 3 Composition API</li>
      <li>📦 开箱即用的高质量组件</li>
      <li>🎨 精美的默认主题设计</li>
      <li>💪 使用 TypeScript 编写，提供完整的类型定义</li>
      <li>📱 支持响应式设计</li>
      <li>🛠️ 支持按需引入</li>
      <li>🌍 支持国际化</li>
    </ul>

    <h2>版本</h2>
    <p>当前版本：<code class="version-tag">v1.0.1</code></p>

    <h2>兼容性</h2>
    <p>支持现代浏览器和 IE11+。</p>
    
    <div class="compatibility-table">
      <table>
        <thead>
          <tr>
            <th>浏览器</th>
            <th>版本</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Chrome</td>
            <td>≥ 85</td>
          </tr>
          <tr>
            <td>Firefox</td>
            <td>≥ 79</td>
          </tr>
          <tr>
            <td>Safari</td>
            <td>≥ 14</td>
          </tr>
          <tr>
            <td>Edge</td>
            <td>≥ 85</td>
          </tr>
        </tbody>
      </table>
    </div>

    <h2>贡献</h2>
    <p>
      如果你在使用过程中发现任何问题，或者有好的建议，欢迎提交 
      <a href="https://github.com/yourusername/officialblock/issues" target="_blank">Issue</a> 
      或者 
      <a href="https://github.com/yourusername/officialblock/pulls" target="_blank">Pull Request</a>。
    </p>
  </div>
</template>

<script setup lang="ts">
// 页面逻辑
</script>

<style scoped>
.page {
  max-width: 800px;
}

.description {
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 32px;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 24px 0;
}

.feature-list li {
  padding: 8px 0;
  font-size: 16px;
  line-height: 1.6;
}

.version-tag {
  background: #f0f9ff;
  color: #409eff;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}

.compatibility-table {
  margin: 24px 0;
}

.compatibility-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.compatibility-table th,
.compatibility-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.compatibility-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.compatibility-table td {
  color: #606266;
}

.compatibility-table tr:last-child td {
  border-bottom: none;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 32px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 16px;
}

a {
  color: #409eff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
}
</style>
