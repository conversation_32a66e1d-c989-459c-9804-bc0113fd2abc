<template>
  <div class="page">
    <h1>快速开始</h1>
    
    <p>本节将介绍如何在项目中使用 OfficialBlock。</p>

    <h2>完整引入</h2>
    <p>如果你对打包后的文件大小不是很在乎，那么使用完整导入会更方便。</p>
    
    <div class="code-block">
      <pre><code>// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)
app.mount('#app')</code></pre>
      <button class="copy-btn" @click="copyCode(fullImportCode)">复制</button>
    </div>

    <h2>按需引入</h2>
    <p>如果你只希望引入部分组件，比如 ArticleList，那么需要在代码中写入以下内容：</p>
    
    <div class="code-block">
      <pre><code>// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.component('ArticleList', ArticleList)
app.component('HeroSlide', HeroSlide)
app.mount('#app')</code></pre>
      <button class="copy-btn" @click="copyCode(partialImportCode)">复制</button>
    </div>

    <h3>在组件中使用</h3>
    <div class="code-block">
      <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;ArticleList v-model="value" size="medium" /&gt;
    &lt;HeroSlide :auto-play="true" /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup lang="ts"&gt;
import { ref } from 'vue'
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const value = ref('Hello World')
&lt;/script&gt;</code></pre>
      <button class="copy-btn" @click="copyCode(componentUsageCode)">复制</button>
    </div>

    <h2>全局配置</h2>
    <p>在引入 OfficialBlock 时，可以传入一个全局配置对象。该对象目前支持 size 与 zIndex 字段。</p>
    
    <div class="code-block">
      <pre><code>import { createApp } from 'vue'
import App from './App.vue'
import OfficialBlock from 'officialblock'

const app = createApp(App)
app.use(OfficialBlock, {
  // 全局组件大小
  size: 'medium',
  // 全局 z-index 配置
  zIndex: 3000,
})
app.mount('#app')</code></pre>
      <button class="copy-btn" @click="copyCode(globalConfigCode)">复制</button>
    </div>

    <h2>开始使用</h2>
    <p>至此，一个基于 Vue 和 OfficialBlock 的开发环境已经搭建完毕，现在就可以编写代码了。各个组件的使用方法请参阅它们各自的文档。</p>

    <div class="next-steps">
      <h3>接下来你可以：</h3>
      <ul>
        <li>
          <router-link to="/components/article-list">查看 ArticleList 组件文档</router-link>
        </li>
        <li>
          <router-link to="/components/hero-slide">查看 HeroSlide 组件文档</router-link>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
const fullImportCode = `// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)
app.mount('#app')`

const partialImportCode = `// main.ts
import { createApp } from 'vue'
import App from './App.vue'
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.component('ArticleList', ArticleList)
app.component('HeroSlide', HeroSlide)
app.mount('#app')`

const componentUsageCode = `<template>
  <div>
    <ArticleList v-model="value" size="medium" />
    <HeroSlide :auto-play="true" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

const value = ref('Hello World')
<\/script>`

const globalConfigCode = `import { createApp } from 'vue'
import App from './App.vue'
import OfficialBlock from 'officialblock'

const app = createApp(App)
app.use(OfficialBlock, {
  // 全局组件大小
  size: 'medium',
  // 全局 z-index 配置
  zIndex: 3000,
})
app.mount('#app')`

const copyCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    console.log('代码已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}
</script>

<style scoped>
.page {
  max-width: 800px;
}

.code-block {
  position: relative;
  margin: 16px 0;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.code-block pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
  background: transparent;
}

.code-block code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
  background: transparent;
}

.copy-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background-color 0.3s;
}

.copy-btn:hover {
  background: #337ecc;
}

.next-steps {
  background: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
  padding: 20px;
  margin: 32px 0;
}

.next-steps h3 {
  margin-top: 0;
  color: #409eff;
}

.next-steps ul {
  margin-bottom: 0;
}

.next-steps a {
  color: #409eff;
  text-decoration: none;
}

.next-steps a:hover {
  text-decoration: underline;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 32px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

h3 {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px 0;
}

p {
  line-height: 1.6;
  color: #606266;
  margin-bottom: 16px;
}

ul {
  color: #606266;
  line-height: 1.6;
}

li {
  margin-bottom: 8px;
}
</style>
