<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>主题系统演示</h1>
      <p>展示项目的主题颜色、字体大小和 UnoCSS 工具类的使用效果。</p>
    </div>

    <!-- 主题控制面板 -->
    <a-card title="主题控制面板" class="demo-section">
      <div class="theme-controls">
        <div class="control-group">
          <h4>预设主题</h4>
          <a-radio-group v-model="selectedPreset" @change="handlePresetChange">
            <a-radio value="default">默认</a-radio>
            <a-radio value="dark">暗色</a-radio>
            <a-radio value="compact">紧凑</a-radio>
            <a-radio value="large">大号</a-radio>
          </a-radio-group>
        </div>

        <div class="control-group">
          <h4>自定义主色调</h4>
          <div class="flex items-center gap-3">
            <input
              type="color"
              v-model="customColor"
              class="color-picker"
              @change="handleCustomColorChange"
            >
            <a-input
              v-model="customColor"
              placeholder="输入颜色值"
              class="w-32"
              @change="handleCustomColorChange"
            />
            <span class="text-sm text-muted">当前: {{ currentTheme.primaryColor }}</span>
          </div>
        </div>

        <div class="control-group">
          <h4>字体大小</h4>
          <div class="flex items-center gap-3">
            <a-select
              v-model="customFontSize"
              class="w-24"
              @change="handleFontSizeChange"
            >
              <a-option value="12px">12px</a-option>
              <a-option value="14px">14px</a-option>
              <a-option value="16px">16px</a-option>
              <a-option value="18px">18px</a-option>
            </a-select>
            <span class="text-sm text-muted">当前: {{ currentTheme.fontSize }}</span>
          </div>
        </div>

        <div class="control-group">
          <h4>模式切换</h4>
          <div class="flex items-center gap-4">
            <a-switch
              v-model="isDarkMode"
              @change="handleDarkModeToggle"
            >
              <template #checked>🌙</template>
              <template #unchecked>☀️</template>
            </a-switch>
            <span class="text-sm text-muted">{{ isDarkMode ? '暗色模式' : '亮色模式' }}</span>
          </div>
        </div>

        <div class="control-group">
          <h4>操作</h4>
          <div class="flex gap-2">
            <a-button type="primary" @click="handleExport">
              导出配置
            </a-button>
            <a-button @click="handleReset">
              重置主题
            </a-button>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 颜色系统演示 -->
    <a-card title="主题颜色系统" class="demo-section">
      <div class="color-grid">
        <!-- 主色调 -->
        <div class="color-group">
          <h3>主色调 (Primary)</h3>
          <div class="color-palette">
            <div class="color-item bg-primary-50 text-primary-900">50</div>
            <div class="color-item bg-primary-100 text-primary-900">100</div>
            <div class="color-item bg-primary-200 text-primary-900">200</div>
            <div class="color-item bg-primary-300 text-primary-900">300</div>
            <div class="color-item bg-primary-400 text-white">400</div>
            <div class="color-item bg-primary-500 text-white font-bold">500</div>
            <div class="color-item bg-primary-600 text-white">600</div>
            <div class="color-item bg-primary-700 text-white">700</div>
            <div class="color-item bg-primary-800 text-white">800</div>
            <div class="color-item bg-primary-900 text-white">900</div>
          </div>
        </div>

        <!-- 辅助色 -->
        <div class="color-group">
          <h3>辅助色 (Secondary)</h3>
          <div class="color-palette">
            <div class="color-item bg-secondary-50 text-secondary-900">50</div>
            <div class="color-item bg-secondary-100 text-secondary-900">100</div>
            <div class="color-item bg-secondary-200 text-secondary-900">200</div>
            <div class="color-item bg-secondary-300 text-secondary-900">300</div>
            <div class="color-item bg-secondary-400 text-white">400</div>
            <div class="color-item bg-secondary-500 text-white font-bold">500</div>
            <div class="color-item bg-secondary-600 text-white">600</div>
            <div class="color-item bg-secondary-700 text-white">700</div>
            <div class="color-item bg-secondary-800 text-white">800</div>
            <div class="color-item bg-secondary-900 text-white">900</div>
          </div>
        </div>

        <!-- 功能色 -->
        <div class="color-group">
          <h3>功能色</h3>
          <div class="functional-colors">
            <div class="color-item bg-success-500 text-white">Success</div>
            <div class="color-item bg-warning-500 text-white">Warning</div>
            <div class="color-item bg-error-500 text-white">Error</div>
            <div class="color-item bg-gray-500 text-white">Gray</div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 字体系统演示 -->
    <a-card title="字体系统" class="demo-section">
      <div class="typography-demo">
        <div class="font-sizes">
          <h3>字体大小</h3>
          <div class="text-xs mb-2">Extra Small (12px) - text-xs</div>
          <div class="text-sm mb-2">Small (14px) - text-sm - 默认字号</div>
          <div class="text-base mb-2">Base (16px) - text-base</div>
          <div class="text-lg mb-2">Large (18px) - text-lg</div>
          <div class="text-xl mb-2">Extra Large (20px) - text-xl</div>
          <div class="text-2xl mb-2">2X Large (24px) - text-2xl</div>
          <div class="text-3xl mb-2">3X Large (30px) - text-3xl</div>
          <div class="text-4xl mb-2">4X Large (36px) - text-4xl</div>
        </div>

        <div class="font-families mt-6">
          <h3>字体族</h3>
          <div class="font-sans mb-2">Sans Serif Font - Inter (font-sans)</div>
          <div class="font-mono mb-2">Monospace Font - JetBrains Mono (font-mono)</div>
        </div>
      </div>
    </a-card>

    <!-- UnoCSS 工具类演示 -->
    <a-card title="UnoCSS 工具类演示" class="demo-section">
      <div class="utilities-demo">
        <!-- 按钮样式 -->
        <div class="utility-group">
          <h3>按钮样式</h3>
          <div class="flex gap-4 flex-wrap">
            <button class="btn-primary">Primary Button</button>
            <button class="btn-secondary">Secondary Button</button>
            <button class="btn-outline">Outline Button</button>
          </div>
        </div>

        <!-- 卡片样式 -->
        <div class="utility-group">
          <h3>卡片样式</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="card p-4">
              <h4 class="text-lg font-semibold mb-2">基础卡片</h4>
              <p class="text-muted">这是一个基础卡片样式，使用了 card 工具类。</p>
            </div>
            <div class="card-hover p-4">
              <h4 class="text-lg font-semibold mb-2">悬停卡片</h4>
              <p class="text-muted">这是一个带悬停效果的卡片，使用了 card-hover 工具类。</p>
            </div>
          </div>
        </div>

        <!-- 输入框样式 -->
        <div class="utility-group">
          <h3>输入框样式</h3>
          <div class="space-y-4">
            <input type="text" class="input w-full" placeholder="使用 input 工具类的输入框">
            <textarea class="input w-full h-24 resize-none" placeholder="文本域示例"></textarea>
          </div>
        </div>

        <!-- 布局工具类 -->
        <div class="utility-group">
          <h3>布局工具类</h3>
          <div class="space-y-4">
            <div class="flex-center h-16 bg-gray-100 rounded">
              <span>flex-center - 居中对齐</span>
            </div>
            <div class="flex-between h-16 bg-gray-100 rounded px-4">
              <span>左侧内容</span>
              <span>flex-between - 两端对齐</span>
            </div>
          </div>
        </div>

        <!-- 文本样式 -->
        <div class="utility-group">
          <h3>文本样式</h3>
          <div class="space-y-2">
            <p class="text-emphasis">强调文本 - text-emphasis</p>
            <p class="text-muted">次要文本 - text-muted</p>
            <p class="text-theme-primary">主题色文本 - text-theme-primary</p>
            <p class="text-default">默认字号文本 - text-default</p>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 响应式演示 -->
    <a-card title="响应式设计" class="demo-section">
      <div class="container-responsive">
        <h3>响应式容器</h3>
        <p class="text-muted mb-4">这个容器使用了 container-responsive 类，会根据屏幕大小自动调整内边距。</p>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <div class="card p-4 text-center" v-for="i in 8" :key="i">
            <div class="w-12 h-12 bg-primary-500 rounded-full mx-auto mb-2"></div>
            <p class="text-sm">响应式网格 {{ i }}</p>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 动画演示 -->
    <a-card title="动画效果" class="demo-section">
      <div class="animation-demo">
        <h3>过渡动画</h3>
        <div class="flex gap-4 flex-wrap">
          <button 
            class="btn-primary transition-fast transform hover:scale-105"
            @click="showMessage('快速过渡')"
          >
            快速过渡 (150ms)
          </button>
          <button 
            class="btn-secondary transition-normal transform hover:scale-105"
            @click="showMessage('正常过渡')"
          >
            正常过渡 (200ms)
          </button>
          <button 
            class="btn-outline transition-slow transform hover:scale-105"
            @click="showMessage('慢速过渡')"
          >
            慢速过渡 (300ms)
          </button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useTheme } from '@/composables/useTheme'

const {
  currentTheme,
  isDarkMode,
  themePresets,
  setTheme,
  updateTheme,
  toggleDarkMode,
  resetTheme,
  exportTheme,
  importTheme,
  initTheme,
  saveTheme
} = useTheme()

const selectedPreset = ref('default')
const customColor = ref('#3b82f6')
const customFontSize = ref('14px')

const showMessage = (content: string) => {
  Message.success(content)
}

const handlePresetChange = (preset: string) => {
  selectedPreset.value = preset
  setTheme(preset as keyof typeof themePresets)
  saveTheme()
  showMessage(`已切换到 ${preset} 主题`)
}

const handleCustomColorChange = () => {
  updateTheme({ primaryColor: customColor.value })
  saveTheme()
  showMessage('主色调已更新')
}

const handleFontSizeChange = () => {
  updateTheme({ fontSize: customFontSize.value })
  saveTheme()
  showMessage('字体大小已更新')
}

const handleDarkModeToggle = () => {
  toggleDarkMode()
  saveTheme()
  showMessage(isDarkMode.value ? '已切换到暗色模式' : '已切换到亮色模式')
}

const handleReset = () => {
  resetTheme()
  selectedPreset.value = 'default'
  customColor.value = '#3b82f6'
  customFontSize.value = '14px'
  saveTheme()
  showMessage('主题已重置')
}

const handleExport = () => {
  const themeConfig = exportTheme()
  navigator.clipboard.writeText(themeConfig).then(() => {
    showMessage('主题配置已复制到剪贴板')
  }).catch(() => {
    console.log('Theme config:', themeConfig)
    showMessage('主题配置已输出到控制台')
  })
}

onMounted(() => {
  initTheme()
  customColor.value = currentTheme.value.primaryColor
  customFontSize.value = currentTheme.value.fontSize
})
</script>

<style scoped>
.demo-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 32px;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--theme-gray-900);
}

.demo-header p {
  margin: 0;
  font-size: 16px;
  color: var(--theme-gray-600);
  line-height: 1.6;
}

.demo-section {
  margin-bottom: 24px;
}

.theme-controls {
  display: grid;
  gap: 24px;
}

.control-group h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-gray-800);
}

.color-picker {
  width: 40px;
  height: 32px;
  border: 1px solid var(--theme-gray-300);
  border-radius: 6px;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

.color-grid {
  display: grid;
  gap: 24px;
}

.color-group h3 {
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 600;
}

.color-palette {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 4px;
  margin-bottom: 16px;
}

.functional-colors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.color-item {
  padding: 12px 8px;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typography-demo {
  display: grid;
  gap: 24px;
}

.utilities-demo {
  display: grid;
  gap: 32px;
}

.utility-group h3 {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

.animation-demo h3 {
  margin-bottom: 16px;
  font-size: 18px;
  font-weight: 600;
}

@media (max-width: 768px) {
  .color-palette {
    grid-template-columns: repeat(5, 1fr);
  }
  
  .functional-colors {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
