<template>
  <div class="sass-demo">
    <div class="demo-header">
      <h1>Sass 使用演示</h1>
      <p>展示如何在项目中使用 Sass 和 @ 路径别名</p>
    </div>

    <!-- Sass 变量演示 -->
    <a-card title="Sass 变量使用" class="demo-section">
      <div class="variables-demo">
        <h3>颜色变量</h3>
        <div class="color-grid">
          <div class="color-item primary">
            <span>$primary-color</span>
          </div>
          <div class="color-item secondary">
            <span>$secondary-color</span>
          </div>
          <div class="color-item success">
            <span>$success-color</span>
          </div>
          <div class="color-item warning">
            <span>$warning-color</span>
          </div>
          <div class="color-item error">
            <span>$error-color</span>
          </div>
        </div>

        <h3>字体变量</h3>
        <div class="font-demo">
          <p class="font-xs">超小字体 ($font-size-xs)</p>
          <p class="font-sm">小字体 ($font-size-sm)</p>
          <p class="font-base">基础字体 ($font-size-base)</p>
          <p class="font-lg">大字体 ($font-size-lg)</p>
          <p class="font-xl">超大字体 ($font-size-xl)</p>
        </div>

        <h3>间距变量</h3>
        <div class="spacing-demo">
          <div class="spacing-item spacing-xs">xs</div>
          <div class="spacing-item spacing-sm">sm</div>
          <div class="spacing-item spacing-md">md</div>
          <div class="spacing-item spacing-lg">lg</div>
          <div class="spacing-item spacing-xl">xl</div>
        </div>
      </div>
    </a-card>

    <!-- Sass 混合器演示 -->
    <a-card title="Sass 混合器 (Mixins)" class="demo-section">
      <div class="mixins-demo">
        <h3>按钮混合器</h3>
        <div class="button-group">
          <button class="btn-primary-mixin">主要按钮</button>
          <button class="btn-secondary-mixin">次要按钮</button>
          <button class="btn-outline-mixin">轮廓按钮</button>
        </div>

        <h3>布局混合器</h3>
        <div class="layout-examples">
          <div class="flex-center-example">
            <span>flex-center 混合器</span>
          </div>
          <div class="flex-between-example">
            <span>左侧内容</span>
            <span>右侧内容</span>
          </div>
        </div>

        <h3>卡片混合器</h3>
        <div class="card-example">
          <h4>卡片标题</h4>
          <p>这是使用 @include card 混合器创建的卡片样式。</p>
        </div>
      </div>
    </a-card>

    <!-- 响应式混合器演示 -->
    <a-card title="响应式混合器" class="demo-section">
      <div class="responsive-demo">
        <div class="responsive-box">
          <h4>响应式盒子</h4>
          <p>这个盒子在不同屏幕尺寸下会显示不同的样式：</p>
          <ul>
            <li>移动端：红色背景</li>
            <li>平板端：蓝色背景</li>
            <li>桌面端：绿色背景</li>
          </ul>
        </div>
      </div>
    </a-card>

    <!-- @ 路径别名演示 -->
    <a-card title="@ 路径别名使用" class="demo-section">
      <div class="alias-demo">
        <h3>导入示例</h3>
        <div class="code-examples">
          <div class="code-block">
            <h4>在 Vue 组件中使用</h4>
            <pre><code>// 导入 Sass 变量
@import "@/styles/variables.scss";

// 导入其他组件
import Button from "@/components/Button/index.vue";

// 导入工具函数
import { useTheme } from "@/composables/useTheme";</code></pre>
          </div>

          <div class="code-block">
            <h4>在 Sass 文件中使用</h4>
            <pre><code>// 导入变量文件
@import "@/styles/variables.scss";

// 导入其他样式文件
@import "@/styles/mixins.scss";
@import "@/styles/utilities.scss";</code></pre>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 实际组件演示 -->
    <a-card title="实际组件演示" class="demo-section">
      <div class="component-demo">
        <h3>Button 组件</h3>
        <p>这个按钮组件使用了 Sass 变量和 @ 路径别名：</p>
        <div class="button-showcase">
          <Button />
        </div>

        <h3>输入框演示</h3>
        <div class="input-showcase">
          <input type="text" class="input-example" placeholder="使用 Sass 混合器的输入框">
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import Button from '@/components/Button/index.vue'
</script>

<style lang="scss" scoped>
// 使用 @ 路径别名导入 Sass 变量
@import '@/styles/variables.scss';

.sass-demo {
  padding: $spacing-lg;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: $spacing-xl;
  text-align: center;

  h1 {
    font-size: $font-size-3xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    margin-bottom: $spacing-sm;
  }

  p {
    font-size: $font-size-lg;
    color: $text-tertiary;
  }
}

.demo-section {
  margin-bottom: $spacing-xl;
}

// 变量演示样式
.variables-demo {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: $spacing-lg 0 $spacing-md 0;
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
}

.color-item {
  height: 80px;
  border-radius: $radius-md;
  @include flex-center;
  color: white;
  font-weight: $font-weight-medium;

  &.primary { background-color: $primary-color; }
  &.secondary { background-color: $secondary-color; }
  &.success { background-color: $success-color; }
  &.warning { background-color: $warning-color; }
  &.error { background-color: $error-color; }
}

.font-demo {
  margin-bottom: $spacing-xl;

  p {
    margin: $spacing-sm 0;
    color: $text-primary;
  }

  .font-xs { font-size: $font-size-xs; }
  .font-sm { font-size: $font-size-sm; }
  .font-base { font-size: $font-size-base; }
  .font-lg { font-size: $font-size-lg; }
  .font-xl { font-size: $font-size-xl; }
}

.spacing-demo {
  display: flex;
  align-items: flex-end;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
}

.spacing-item {
  background-color: $primary-color;
  color: white;
  @include flex-center;
  font-weight: $font-weight-medium;
  border-radius: $radius-sm;

  &.spacing-xs { padding: $spacing-xs; }
  &.spacing-sm { padding: $spacing-sm; }
  &.spacing-md { padding: $spacing-md; }
  &.spacing-lg { padding: $spacing-lg; }
  &.spacing-xl { padding: $spacing-xl; }
}

// 混合器演示样式
.mixins-demo {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: $spacing-lg 0 $spacing-md 0;
  }
}

.button-group {
  display: flex;
  gap: $spacing-md;
  margin-bottom: $spacing-xl;
  flex-wrap: wrap;
}

.btn-primary-mixin {
  @include button-primary;
}

.btn-secondary-mixin {
  @include button-secondary;
}

.btn-outline-mixin {
  @include button-outline;
}

.layout-examples {
  margin-bottom: $spacing-xl;
}

.flex-center-example {
  @include flex-center;
  height: 80px;
  background-color: $gray-100;
  border-radius: $radius-md;
  margin-bottom: $spacing-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

.flex-between-example {
  @include flex-between;
  padding: $spacing-md;
  background-color: $gray-100;
  border-radius: $radius-md;
  font-weight: $font-weight-medium;
  color: $text-primary;
}

.card-example {
  @include card;
  margin-bottom: $spacing-xl;

  h4 {
    margin: 0 0 $spacing-sm 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }

  p {
    margin: 0;
    color: $text-tertiary;
    line-height: $line-height-normal;
  }
}

// 响应式演示样式
.responsive-box {
  padding: $spacing-lg;
  border-radius: $radius-md;
  background-color: $gray-100;

  @include mobile {
    background-color: lighten($error-color, 20%);
    color: $error-color;
  }

  @include tablet {
    background-color: lighten($primary-color, 20%);
    color: $primary-color;
  }

  @include desktop {
    background-color: lighten($success-color, 20%);
    color: $success-color;
  }

  h4 {
    margin: 0 0 $spacing-sm 0;
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
  }

  p {
    margin: 0 0 $spacing-sm 0;
    line-height: $line-height-normal;
  }

  ul {
    margin: 0;
    padding-left: $spacing-lg;
  }

  li {
    margin-bottom: $spacing-xs;
  }
}

// 代码示例样式
.alias-demo {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: $spacing-lg 0 $spacing-md 0;
  }
}

.code-examples {
  display: grid;
  gap: $spacing-lg;

  @include tablet-up {
    grid-template-columns: 1fr 1fr;
  }
}

.code-block {
  h4 {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: 0 0 $spacing-sm 0;
  }

  pre {
    background-color: $gray-900;
    color: $gray-100;
    padding: $spacing-md;
    border-radius: $radius-md;
    overflow-x: auto;
    font-family: $font-family-mono;
    font-size: $font-size-sm;
    line-height: $line-height-normal;
    margin: 0;
  }
}

// 组件演示样式
.component-demo {
  h3 {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin: $spacing-lg 0 $spacing-md 0;
  }

  p {
    color: $text-tertiary;
    margin-bottom: $spacing-md;
  }
}

.button-showcase {
  margin-bottom: $spacing-xl;
}

.input-showcase {
  .input-example {
    @include input;
    max-width: 400px;
  }
}
</style>
