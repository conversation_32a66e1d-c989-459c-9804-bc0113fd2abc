<template>
  <div class="demo-page">
    <h1>HeroSlide 轮播图</h1>
    <p class="description">
      HeroSlide 组件用于展示轮播图，支持自动播放、指示器显示等功能。
    </p>

    <h2>基础用法</h2>
    <div class="demo-section">
      <div class="demo-container">
        <HeroSlide />
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;HeroSlide /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { HeroSlide } from 'officialblock'
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <h2>自定义配置</h2>
    <div class="demo-section">
      <div class="demo-container">
        <HeroSlide 
          :auto-play-interval="2000"
          :show-indicators="true"
          :auto-play="autoPlay"
          @change="handleSlideChange"
        />
        <div class="controls">
          <button @click="toggleAutoPlay">
            {{ autoPlay ? '停止自动播放' : '开始自动播放' }}
          </button>
        </div>
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;HeroSlide 
    :auto-play-interval="2000"
    :show-indicators="true"
    :auto-play="autoPlay"
    @change="handleSlideChange"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import { HeroSlide } from 'officialblock'

const autoPlay = ref(true)

const handleSlideChange = (index) => {
  console.log('切换到第', index + 1, '张')
}
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <h2>隐藏指示器</h2>
    <div class="demo-section">
      <div class="demo-container">
        <HeroSlide 
          :show-indicators="false"
          :auto-play="true"
          :auto-play-interval="3000"
        />
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;HeroSlide 
    :show-indicators="false"
    :auto-play="true"
    :auto-play-interval="3000"
  /&gt;
&lt;/template&gt;</code></pre>
      </div>
    </div>

    <h2>事件处理</h2>
    <div class="demo-section">
      <div class="demo-container">
        <HeroSlide 
          @change="handleChange"
          @click="handleClick"
        />
        <div class="event-log">
          <h4>事件日志:</h4>
          <ul>
            <li v-for="(log, index) in eventLogs" :key="index">{{ log }}</li>
          </ul>
        </div>
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;HeroSlide 
    @change="handleChange"
    @click="handleClick"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
const handleChange = (index) => {
  console.log('幻灯片切换:', index)
}

const handleClick = (index) => {
  console.log('点击幻灯片:', index)
}
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <h2>API</h2>
    <div class="api-section">
      <h3>Props</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>参数</th>
            <th>说明</th>
            <th>类型</th>
            <th>可选值</th>
            <th>默认值</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>autoPlayInterval</td>
            <td>自动播放间隔时间（毫秒）</td>
            <td>number</td>
            <td>—</td>
            <td>3000</td>
          </tr>
          <tr>
            <td>showIndicators</td>
            <td>是否显示指示器</td>
            <td>boolean</td>
            <td>—</td>
            <td>true</td>
          </tr>
          <tr>
            <td>autoPlay</td>
            <td>是否启用自动播放</td>
            <td>boolean</td>
            <td>—</td>
            <td>true</td>
          </tr>
        </tbody>
      </table>

      <h3>Events</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>事件名</th>
            <th>说明</th>
            <th>回调参数</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>change</td>
            <td>幻灯片切换时触发</td>
            <td>(index: number)</td>
          </tr>
          <tr>
            <td>click</td>
            <td>点击幻灯片时触发</td>
            <td>(index: number)</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { HeroSlide } from '../../components'

const autoPlay = ref(true)
const eventLogs = ref<string[]>([])

const toggleAutoPlay = () => {
  autoPlay.value = !autoPlay.value
}

const handleSlideChange = (index: number) => {
  console.log('切换到第', index + 1, '张')
}

const handleChange = (index: number) => {
  eventLogs.value.unshift(`change: 切换到第 ${index + 1} 张 (${new Date().toLocaleTimeString()})`)
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}

const handleClick = (index: number) => {
  eventLogs.value.unshift(`click: 点击第 ${index + 1} 张 (${new Date().toLocaleTimeString()})`)
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}
</script>

<style scoped>
.demo-page {
  max-width: 1000px;
}

.description {
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 32px;
}

.demo-section {
  margin: 32px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.demo-container {
  padding: 24px;
  background: #fff;
}

.demo-code {
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.demo-code pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.demo-code code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
}

.controls {
  margin-top: 16px;
  text-align: center;
}

.controls button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.controls button:hover {
  background: #337ecc;
}

.event-log {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.event-log h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.event-log ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.event-log li {
  padding: 4px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
}

.api-section {
  margin: 48px 0;
}

.api-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin: 16px 0;
}

.api-table th,
.api-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.api-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.api-table td {
  color: #606266;
}

.api-table tr:last-child td {
  border-bottom: none;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 48px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

h3 {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px 0;
}
</style>
