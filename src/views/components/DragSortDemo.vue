<template>
  <div class="drag-sort-demo">
    <div class="demo-header">
      <h1>拖拽排序演示</h1>
      <p>展示如何使用 vuedraggable 实现列表项的拖拽排序功能</p>
    </div>

    <!-- 基础拖拽排序 -->
    <a-card title="基础拖拽排序" class="demo-section">
      <div class="basic-drag-demo">
        <h3>简单列表拖拽</h3>
        <draggable 
          v-model="basicList" 
          :component-data="{
            tag: 'div',
            type: 'transition-group',
            name: !drag ? 'flip-list' : null
          }"
          v-bind="dragOptions"
          @start="drag = true"
          @end="drag = false"
          item-key="id"
          class="drag-area"
        >
          <template #item="{ element: item }">
            <div class="drag-item" :key="item.id">
              <div class="drag-handle">
                <icon-drag-arrow class="drag-icon" />
              </div>
              <div class="item-content">
                <span class="item-text">{{ item.name }}</span>
                <span class="item-order">顺序: {{ item.order }}</span>
              </div>
              <button class="delete-btn" @click="removeItem(basicList, item.id)">
                <icon-delete />
              </button>
            </div>
          </template>
        </draggable>
        
        <button class="add-btn" @click="addBasicItem">
          <icon-plus /> 添加项目
        </button>
      </div>
    </a-card>

    <!-- 复杂拖拽排序 -->
    <a-card title="复杂拖拽排序" class="demo-section">
      <div class="complex-drag-demo">
        <h3>带表单的拖拽列表</h3>
        <draggable 
          v-model="complexList" 
          :component-data="{
            tag: 'div',
            type: 'transition-group',
            name: !drag ? 'flip-list' : null
          }"
          v-bind="dragOptions"
          @start="drag = true"
          @end="drag = false"
          item-key="id"
          class="drag-area"
        >
          <template #item="{ element: item }">
            <div class="complex-drag-item" :key="item.id">
              <div class="drag-handle">
                <icon-drag-arrow class="drag-icon" />
              </div>
              <div class="item-form">
                <div class="form-row">
                  <label>标题:</label>
                  <a-input v-model="item.title" placeholder="请输入标题" />
                </div>
                <div class="form-row">
                  <label>描述:</label>
                  <a-textarea v-model="item.description" placeholder="请输入描述" :rows="2" />
                </div>
                <div class="form-row">
                  <label>链接:</label>
                  <a-input v-model="item.url" placeholder="请输入链接" />
                </div>
                <div class="form-row">
                  <a-checkbox v-model="item.isActive">启用</a-checkbox>
                </div>
              </div>
              <button class="delete-btn" @click="removeItem(complexList, item.id)">
                <icon-delete />
              </button>
            </div>
          </template>
        </draggable>
        
        <button class="add-btn" @click="addComplexItem">
          <icon-plus /> 添加复杂项目
        </button>
      </div>
    </a-card>

    <!-- 分组拖拽排序 -->
    <a-card title="分组拖拽排序" class="demo-section">
      <div class="group-drag-demo">
        <div class="drag-groups">
          <div class="drag-group">
            <h4>待办事项</h4>
            <draggable 
              v-model="todoList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="groupDragOptions"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area group-area"
            >
              <template #item="{ element: item }">
                <div class="group-drag-item todo-item" :key="item.id">
                  <div class="drag-handle">
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <span>{{ item.text }}</span>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
          
          <div class="drag-group">
            <h4>进行中</h4>
            <draggable 
              v-model="doingList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="groupDragOptions"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area group-area"
            >
              <template #item="{ element: item }">
                <div class="group-drag-item doing-item" :key="item.id">
                  <div class="drag-handle">
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <span>{{ item.text }}</span>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
          
          <div class="drag-group">
            <h4>已完成</h4>
            <draggable 
              v-model="doneList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="groupDragOptions"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area group-area"
            >
              <template #item="{ element: item }">
                <div class="group-drag-item done-item" :key="item.id">
                  <div class="drag-handle">
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <span>{{ item.text }}</span>
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 使用说明 -->
    <a-card title="使用说明" class="demo-section">
      <div class="usage-guide">
        <h3>如何使用拖拽排序</h3>
        <ol>
          <li>安装 vuedraggable: <code>npm install vuedraggable@next</code></li>
          <li>在组件中导入: <code>import draggable from 'vuedraggable'</code></li>
          <li>使用 draggable 组件包装列表</li>
          <li>配置拖拽选项和事件处理</li>
          <li>添加适当的样式和动画效果</li>
        </ol>
        
        <h3>主要配置选项</h3>
        <ul>
          <li><strong>v-model</strong>: 绑定的数组数据</li>
          <li><strong>item-key</strong>: 列表项的唯一标识字段</li>
          <li><strong>animation</strong>: 动画持续时间</li>
          <li><strong>group</strong>: 分组名称，相同组可以互相拖拽</li>
          <li><strong>disabled</strong>: 是否禁用拖拽</li>
          <li><strong>ghostClass</strong>: 拖拽时的幽灵样式类</li>
        </ul>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import draggable from 'vuedraggable'

// 拖拽状态
const drag = ref(false)

// 基础拖拽配置
const dragOptions = computed(() => ({
  animation: 200,
  group: 'description',
  disabled: false,
  ghostClass: 'ghost'
}))

// 分组拖拽配置
const groupDragOptions = computed(() => ({
  animation: 200,
  group: 'shared',
  disabled: false,
  ghostClass: 'ghost'
}))

// 基础列表数据
const basicList = ref([
  { id: 1, name: '项目 1', order: 1 },
  { id: 2, name: '项目 2', order: 2 },
  { id: 3, name: '项目 3', order: 3 },
  { id: 4, name: '项目 4', order: 4 }
])

// 复杂列表数据
const complexList = ref([
  {
    id: 1,
    title: '第一个项目',
    description: '这是第一个项目的描述',
    url: 'https://example.com/1',
    isActive: true
  },
  {
    id: 2,
    title: '第二个项目',
    description: '这是第二个项目的描述',
    url: 'https://example.com/2',
    isActive: false
  }
])

// 分组列表数据
const todoList = ref([
  { id: 1, text: '学习 Vue 3' },
  { id: 2, text: '完成项目文档' }
])

const doingList = ref([
  { id: 3, text: '开发新功能' },
  { id: 4, text: '修复 Bug' }
])

const doneList = ref([
  { id: 5, text: '代码审查' },
  { id: 6, text: '部署到生产环境' }
])

// 添加基础项目
const addBasicItem = () => {
  const newId = Math.max(...basicList.value.map(item => item.id)) + 1
  basicList.value.push({
    id: newId,
    name: `项目 ${newId}`,
    order: newId
  })
}

// 添加复杂项目
const addComplexItem = () => {
  const newId = Math.max(...complexList.value.map(item => item.id)) + 1
  complexList.value.push({
    id: newId,
    title: `新项目 ${newId}`,
    description: '请输入项目描述',
    url: '',
    isActive: true
  })
}

// 删除项目
const removeItem = (list: any[], id: number) => {
  const index = list.findIndex(item => item.id === id)
  if (index > -1) {
    list.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
.drag-sort-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 32px;
  text-align: center;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 8px;
  }

  p {
    font-size: 16px;
    color: #6b7280;
  }
}

.demo-section {
  margin-bottom: 32px;
}

// 拖拽区域样式
.drag-area {
  min-height: 100px;
  padding: 16px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
}

// 基础拖拽项样式
.drag-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: move;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .drag-handle {
    margin-right: 12px;
    cursor: grab;
    color: #9ca3af;

    &:active {
      cursor: grabbing;
    }

    .drag-icon {
      font-size: 16px;
    }
  }

  .item-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item-text {
      font-weight: 500;
      color: #1f2937;
    }

    .item-order {
      font-size: 12px;
      color: #6b7280;
    }
  }

  .delete-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: #fee2e2;
    }
  }
}

// 复杂拖拽项样式
.complex-drag-item {
  display: flex;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: move;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .drag-handle {
    margin-right: 16px;
    cursor: grab;
    color: #9ca3af;
    align-self: flex-start;
    margin-top: 8px;

    &:active {
      cursor: grabbing;
    }
  }

  .item-form {
    flex: 1;

    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      label {
        width: 60px;
        margin-right: 12px;
        font-weight: 500;
        color: #374151;
      }

      .arco-input,
      .arco-textarea {
        flex: 1;
      }
    }
  }

  .delete-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    align-self: flex-start;

    &:hover {
      background-color: #fee2e2;
    }
  }
}

// 分组拖拽样式
.drag-groups {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.drag-group {
  h4 {
    margin-bottom: 16px;
    font-weight: 600;
    color: #1f2937;
    text-align: center;
  }

  .group-area {
    min-height: 200px;
  }
}

.group-drag-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  cursor: move;
  transition: all 0.3s ease;

  &.todo-item {
    background: #fef3c7;
    border-left: 4px solid #f59e0b;
  }

  &.doing-item {
    background: #dbeafe;
    border-left: 4px solid #3b82f6;
  }

  &.done-item {
    background: #d1fae5;
    border-left: 4px solid #10b981;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .drag-handle {
    margin-right: 8px;
    cursor: grab;
    color: #6b7280;

    &:active {
      cursor: grabbing;
    }
  }

  .item-content {
    flex: 1;
    font-weight: 500;
  }
}

// 添加按钮样式
.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  margin-top: 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background: #2563eb;
  }
}

// 拖拽动画
.flip-list-move {
  transition: transform 0.5s;
}

.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

// 使用说明样式
.usage-guide {
  h3 {
    margin-bottom: 16px;
    font-weight: 600;
    color: #1f2937;
  }

  ol, ul {
    margin-bottom: 24px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
      line-height: 1.6;
    }
  }

  code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 14px;
  }

  strong {
    font-weight: 600;
    color: #1f2937;
  }
}
</style>
