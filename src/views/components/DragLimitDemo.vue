<template>
  <div class="drag-limit-demo">
    <div class="demo-header">
      <h1>拖拽限制演示</h1>
      <p>展示不同分组的拖拽限制和单项隐藏拖拽图标的功能</p>
    </div>

    <!-- 分组隔离演示 -->
    <a-card title="分组隔离演示" class="demo-section">
      <div class="group-isolation-demo">
        <div class="drag-groups">
          <div class="drag-group">
            <h4>按钮组 (buttons)</h4>
            <p class="group-desc">只能在按钮组内拖拽排序</p>
            <draggable 
              v-model="buttonList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="buttonDragOptions"
              :disabled="!shouldShowDragHandle(buttonList)"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area"
            >
              <template #item="{ element: button }">
                <div 
                  class="drag-item button-item" 
                  :class="{ 'sortable-disabled': !shouldShowDragHandle(buttonList) }"
                  :key="button.id"
                >
                  <div 
                    v-if="shouldShowDragHandle(buttonList)" 
                    class="drag-handle"
                  >
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <a-button type="primary">{{ button.text }}</a-button>
                  </div>
                  <button class="delete-btn" @click="removeItem(buttonList, button.id)">
                    <icon-delete />
                  </button>
                </div>
              </template>
            </draggable>
            <button class="add-btn" @click="addButton">
              <icon-plus /> 添加按钮
            </button>
          </div>
          
          <div class="drag-group">
            <h4>链接组 (links)</h4>
            <p class="group-desc">只能在链接组内拖拽排序</p>
            <draggable 
              v-model="linkList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="linkDragOptions"
              :disabled="!shouldShowDragHandle(linkList)"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area"
            >
              <template #item="{ element: link }">
                <div 
                  class="drag-item link-item" 
                  :class="{ 'sortable-disabled': !shouldShowDragHandle(linkList) }"
                  :key="link.id"
                >
                  <div 
                    v-if="shouldShowDragHandle(linkList)" 
                    class="drag-handle"
                  >
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <a-link :href="link.url">{{ link.text }}</a-link>
                  </div>
                  <button class="delete-btn" @click="removeItem(linkList, link.id)">
                    <icon-delete />
                  </button>
                </div>
              </template>
            </draggable>
            <button class="add-btn" @click="addLink">
              <icon-plus /> 添加链接
            </button>
          </div>
          
          <div class="drag-group">
            <h4>分类组 (categories)</h4>
            <p class="group-desc">只能在分类组内拖拽排序</p>
            <draggable 
              v-model="categoryList" 
              :component-data="{
                tag: 'div',
                type: 'transition-group',
                name: !drag ? 'flip-list' : null
              }"
              v-bind="categoryDragOptions"
              :disabled="!shouldShowDragHandle(categoryList)"
              @start="drag = true"
              @end="drag = false"
              item-key="id"
              class="drag-area"
            >
              <template #item="{ element: category }">
                <div 
                  class="drag-item category-item" 
                  :class="{ 'sortable-disabled': !shouldShowDragHandle(categoryList) }"
                  :key="category.id"
                >
                  <div 
                    v-if="shouldShowDragHandle(categoryList)" 
                    class="drag-handle"
                  >
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <a-tag color="blue">{{ category.text }}</a-tag>
                  </div>
                  <button class="delete-btn" @click="removeItem(categoryList, category.id)">
                    <icon-delete />
                  </button>
                </div>
              </template>
            </draggable>
            <button class="add-btn" @click="addCategory">
              <icon-plus /> 添加分类
            </button>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 单项隐藏演示 -->
    <a-card title="单项隐藏演示" class="demo-section">
      <div class="single-item-demo">
        <p class="demo-desc">当列表只有一项时，拖拽图标会自动隐藏，且不可拖拽</p>
        
        <div class="single-item-groups">
          <div class="single-group">
            <h4>单个按钮 (无拖拽图标)</h4>
            <draggable 
              v-model="singleButtonList" 
              v-bind="buttonDragOptions"
              :disabled="!shouldShowDragHandle(singleButtonList)"
              item-key="id"
              class="drag-area"
            >
              <template #item="{ element: button }">
                <div 
                  class="drag-item button-item" 
                  :class="{ 'sortable-disabled': !shouldShowDragHandle(singleButtonList) }"
                  :key="button.id"
                >
                  <div 
                    v-if="shouldShowDragHandle(singleButtonList)" 
                    class="drag-handle"
                  >
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <a-button type="primary">{{ button.text }}</a-button>
                  </div>
                  <button class="delete-btn" @click="removeItem(singleButtonList, button.id)">
                    <icon-delete />
                  </button>
                </div>
              </template>
            </draggable>
            <button class="add-btn" @click="addSingleButton">
              <icon-plus /> 添加按钮
            </button>
          </div>
          
          <div class="single-group">
            <h4>单个链接 (无拖拽图标)</h4>
            <draggable 
              v-model="singleLinkList" 
              v-bind="linkDragOptions"
              :disabled="!shouldShowDragHandle(singleLinkList)"
              item-key="id"
              class="drag-area"
            >
              <template #item="{ element: link }">
                <div 
                  class="drag-item link-item" 
                  :class="{ 'sortable-disabled': !shouldShowDragHandle(singleLinkList) }"
                  :key="link.id"
                >
                  <div 
                    v-if="shouldShowDragHandle(singleLinkList)" 
                    class="drag-handle"
                  >
                    <icon-drag-arrow class="drag-icon" />
                  </div>
                  <div class="item-content">
                    <a-link :href="link.url">{{ link.text }}</a-link>
                  </div>
                  <button class="delete-btn" @click="removeItem(singleLinkList, link.id)">
                    <icon-delete />
                  </button>
                </div>
              </template>
            </draggable>
            <button class="add-btn" @click="addSingleLink">
              <icon-plus /> 添加链接
            </button>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 功能说明 -->
    <a-card title="功能说明" class="demo-section">
      <div class="feature-explanation">
        <h3>拖拽限制功能</h3>
        <ul>
          <li><strong>分组隔离</strong>：不同分组的列表项不能互相拖拽</li>
          <li><strong>单项隐藏</strong>：当列表只有一项时，自动隐藏拖拽图标</li>
          <li><strong>禁用拖拽</strong>：单项列表不可拖拽，避免无意义的拖拽操作</li>
          <li><strong>视觉反馈</strong>：禁用状态下移除悬停效果</li>
        </ul>
        
        <h3>实现原理</h3>
        <ul>
          <li><strong>分组配置</strong>：为不同类型的列表设置不同的 group 名称</li>
          <li><strong>条件渲染</strong>：使用 v-if 条件渲染拖拽图标</li>
          <li><strong>动态禁用</strong>：根据列表长度动态设置 disabled 属性</li>
          <li><strong>样式控制</strong>：通过 CSS 类控制拖拽状态的视觉效果</li>
        </ul>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import draggable from 'vuedraggable'

// 拖拽状态
const drag = ref(false)

// 不同类型的拖拽配置选项
const buttonDragOptions = computed(() => ({
  animation: 200,
  group: 'buttons', // 按钮专用分组
  disabled: false,
  ghostClass: 'ghost'
}))

const linkDragOptions = computed(() => ({
  animation: 200,
  group: 'links', // 链接专用分组
  disabled: false,
  ghostClass: 'ghost'
}))

const categoryDragOptions = computed(() => ({
  animation: 200,
  group: 'categories', // 分类专用分组
  disabled: false,
  ghostClass: 'ghost'
}))

// 检查是否应该显示拖拽图标
const shouldShowDragHandle = (list: any[]) => {
  return list && list.length > 1
}

// 分组列表数据
const buttonList = ref([
  { id: 1, text: '按钮 1' },
  { id: 2, text: '按钮 2' },
  { id: 3, text: '按钮 3' }
])

const linkList = ref([
  { id: 1, text: '链接 1', url: 'https://example.com/1' },
  { id: 2, text: '链接 2', url: 'https://example.com/2' }
])

const categoryList = ref([
  { id: 1, text: '分类 1' },
  { id: 2, text: '分类 2' },
  { id: 3, text: '分类 3' }
])

// 单项列表数据
const singleButtonList = ref([
  { id: 1, text: '单个按钮' }
])

const singleLinkList = ref([
  { id: 1, text: '单个链接', url: 'https://example.com' }
])

// 添加项目的方法
const addButton = () => {
  const newId = Math.max(...buttonList.value.map(item => item.id)) + 1
  buttonList.value.push({
    id: newId,
    text: `按钮 ${newId}`
  })
}

const addLink = () => {
  const newId = Math.max(...linkList.value.map(item => item.id)) + 1
  linkList.value.push({
    id: newId,
    text: `链接 ${newId}`,
    url: `https://example.com/${newId}`
  })
}

const addCategory = () => {
  const newId = Math.max(...categoryList.value.map(item => item.id)) + 1
  categoryList.value.push({
    id: newId,
    text: `分类 ${newId}`
  })
}

const addSingleButton = () => {
  const newId = Math.max(...singleButtonList.value.map(item => item.id)) + 1
  singleButtonList.value.push({
    id: newId,
    text: `按钮 ${newId}`
  })
}

const addSingleLink = () => {
  const newId = Math.max(...singleLinkList.value.map(item => item.id)) + 1
  singleLinkList.value.push({
    id: newId,
    text: `链接 ${newId}`,
    url: `https://example.com/${newId}`
  })
}

// 删除项目
const removeItem = (list: any[], id: number) => {
  const index = list.findIndex(item => item.id === id)
  if (index > -1) {
    list.splice(index, 1)
  }
}
</script>

<style lang="scss" scoped>
.drag-limit-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 32px;
  text-align: center;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 8px;
  }

  p {
    font-size: 16px;
    color: #6b7280;
  }
}

.demo-section {
  margin-bottom: 32px;
}

// 分组演示样式
.drag-groups {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.drag-group {
  h4 {
    margin-bottom: 8px;
    font-weight: 600;
    color: #1f2937;
  }

  .group-desc {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 16px;
  }
}

// 单项演示样式
.single-item-groups {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.single-group {
  h4 {
    margin-bottom: 16px;
    font-weight: 600;
    color: #1f2937;
  }
}

// 拖拽区域样式
.drag-area {
  min-height: 80px;
  padding: 16px;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  background-color: #f9fafb;
  margin-bottom: 16px;
}

// 拖拽项样式
.drag-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: move;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  &.sortable-disabled {
    cursor: default;
    
    &:hover {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transform: none;
    }
  }

  .drag-handle {
    margin-right: 12px;
    cursor: grab;
    color: #9ca3af;

    &:active {
      cursor: grabbing;
    }

    .drag-icon {
      font-size: 16px;
    }
  }

  .item-content {
    flex: 1;
  }

  .delete-btn {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: #fee2e2;
    }
  }
}

// 不同类型项目的样式
.button-item {
  border-left: 4px solid #3b82f6;
}

.link-item {
  border-left: 4px solid #10b981;
}

.category-item {
  border-left: 4px solid #f59e0b;
}

// 添加按钮样式
.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background: #2563eb;
  }
}

// 拖拽动画
.flip-list-move {
  transition: transform 0.5s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

// 功能说明样式
.demo-desc {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 24px;
  padding: 12px;
  background-color: #f3f4f6;
  border-radius: 6px;
}

.feature-explanation {
  h3 {
    margin-bottom: 16px;
    font-weight: 600;
    color: #1f2937;
  }

  ul {
    margin-bottom: 24px;
    padding-left: 24px;

    li {
      margin-bottom: 8px;
      line-height: 1.6;

      strong {
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}
</style>
