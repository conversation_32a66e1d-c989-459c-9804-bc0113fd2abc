<template>
  <div class="demo-page">
    <Button></Button>
    <Media></Media>
    <h1>ArticleList 文章列表</h1>
    <p class="description">
      ArticleList 组件用于展示文章列表，支持多种尺寸和状态配置。
    </p>

    <h2>基础用法</h2>
    <div class="demo-section">
      <div class="demo-container">
        <ArticleList v-model="basicValue" />
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;ArticleList v-model="value" /&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import { ArticleList } from 'officialblock'

const value = ref('{{ basicValue }}')
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <h2>不同尺寸</h2>
    <div class="demo-section">
      <div class="demo-container">
        <div class="size-demo">
          <div class="size-item">
            <label>Small:</label>
            <ArticleList v-model="sizeValue" size="small" />
          </div>
          <div class="size-item">
            <label>Medium:</label>
            <ArticleList v-model="sizeValue" size="medium" />
          </div>
          <div class="size-item">
            <label>Large:</label>
            <ArticleList v-model="sizeValue" size="large" />
          </div>
        </div>
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;ArticleList v-model="value" size="small" /&gt;
  &lt;ArticleList v-model="value" size="medium" /&gt;
  &lt;ArticleList v-model="value" size="large" /&gt;
&lt;/template&gt;</code></pre>
      </div>
    </div>

    <h2>禁用状态</h2>
    <div class="demo-section">
      <div class="demo-container">
        <ArticleList v-model="disabledValue" :disabled="true" />
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;ArticleList v-model="value" :disabled="true" /&gt;
&lt;/template&gt;</code></pre>
      </div>
    </div>

    <h2>事件处理</h2>
    <div class="demo-section">
      <div class="demo-container">
        <ArticleList 
          v-model="eventValue" 
          @change="handleChange"
          @focus="handleFocus"
        />
        <div class="event-log">
          <h4>事件日志:</h4>
          <ul>
            <li v-for="(log, index) in eventLogs" :key="index">{{ log }}</li>
          </ul>
        </div>
      </div>
      <div class="demo-code">
        <pre><code>&lt;template&gt;
  &lt;ArticleList 
    v-model="value" 
    @change="handleChange"
    @focus="handleFocus"
  /&gt;
&lt;/template&gt;

&lt;script setup&gt;
const handleChange = (value) => {
  console.log('值改变:', value)
}

const handleFocus = (event) => {
  console.log('获得焦点:', event)
}
&lt;/script&gt;</code></pre>
      </div>
    </div>

    <h2>API</h2>
    <div class="api-section">
      <h3>Props</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>参数</th>
            <th>说明</th>
            <th>类型</th>
            <th>可选值</th>
            <th>默认值</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>modelValue</td>
            <td>双向绑定的值</td>
            <td>string | number</td>
            <td>—</td>
            <td>—</td>
          </tr>
          <tr>
            <td>size</td>
            <td>组件尺寸</td>
            <td>string</td>
            <td>small / medium / large</td>
            <td>medium</td>
          </tr>
          <tr>
            <td>disabled</td>
            <td>是否禁用</td>
            <td>boolean</td>
            <td>—</td>
            <td>false</td>
          </tr>
        </tbody>
      </table>

      <h3>Events</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>事件名</th>
            <th>说明</th>
            <th>回调参数</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>change</td>
            <td>值改变时触发</td>
            <td>(value: string | number)</td>
          </tr>
          <tr>
            <td>focus</td>
            <td>获得焦点时触发</td>
            <td>(event: FocusEvent)</td>
          </tr>
          <tr>
            <td>update:modelValue</td>
            <td>v-model 更新事件</td>
            <td>(value: string | number)</td>
          </tr>
        </tbody>
      </table>

      <h3>Slots</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>插槽名</th>
            <th>说明</th>
            <th>参数</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>default</td>
            <td>默认内容</td>
            <td>{ value: string | number }</td>
          </tr>
          <tr>
            <td>header</td>
            <td>头部内容</td>
            <td>{ title: string }</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ArticleList from '@/components/ArticleList/index.vue'
import Button from '@/components/Button/index.vue'
import Media from '@/components/Media/index.vue'

const basicValue = ref('基础示例文本')
const sizeValue = ref('尺寸示例')
const disabledValue = ref('禁用状态示例')
const eventValue = ref('事件示例')
const eventLogs = ref<string[]>([])

const handleChange = (value: string | number) => {
  eventLogs.value.unshift(`change: ${value} (${new Date().toLocaleTimeString()})`)
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}

const handleFocus = (event: FocusEvent) => {
  eventLogs.value.unshift(`focus: ${event.type} (${new Date().toLocaleTimeString()})`)
  if (eventLogs.value.length > 5) {
    eventLogs.value.pop()
  }
}
</script>

<style scoped>
.demo-page {
  max-width: 1000px;
}

.description {
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 32px;
}

.demo-section {
  margin: 32px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.demo-container {
  padding: 24px;
  background: #fff;
}

.demo-code {
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.demo-code pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.demo-code code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
}

.size-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.size-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-item label {
  width: 80px;
  font-weight: 500;
  color: #303133;
}

.event-log {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.event-log h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.event-log ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.event-log li {
  padding: 4px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
}

.api-section {
  margin: 48px 0;
}

.api-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin: 16px 0;
}

.api-table th,
.api-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.api-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.api-table td {
  color: #606266;
}

.api-table tr:last-child td {
  border-bottom: none;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 48px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

h3 {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px 0;
}
</style>
