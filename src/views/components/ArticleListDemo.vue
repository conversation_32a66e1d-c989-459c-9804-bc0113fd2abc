<template>
  <div class="demo-page">
    <ArticleList
      v-model="articleListData"
      @handle-copy="handleCopy"
      @handle-delete="handleDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ArticleList from '@/components/ArticleList/index.vue'

const articleListData = ref()

const handleCopy = () => {
  console.log('handleCopy')
}

const handleDelete = () => {
  console.log('handleDelete')
}
</script>

<style scoped>
.demo-page {
  background: #fff;
}
.description {
  font-size: 16px;
  line-height: 1.6;
  color: #606266;
  margin-bottom: 32px;
}

.demo-section {
  margin: 32px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.demo-container {
  padding: 24px;
  background: #fff;
}

.demo-code {
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

.demo-code pre {
  margin: 0;
  padding: 20px;
  overflow-x: auto;
}

.demo-code code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
}

.size-demo {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.size-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.size-item label {
  width: 80px;
  font-weight: 500;
  color: #303133;
}

.event-log {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.event-log h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.event-log ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.event-log li {
  padding: 4px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #606266;
}

.api-section {
  margin: 48px 0;
}

.api-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  margin: 16px 0;
}

.api-table th,
.api-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.api-table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #303133;
}

.api-table td {
  color: #606266;
}

.api-table tr:last-child td {
  border-bottom: none;
}

h1 {
  color: #303133;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 24px;
}

h2 {
  color: #303133;
  font-size: 24px;
  font-weight: 600;
  margin: 48px 0 16px 0;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

h3 {
  color: #303133;
  font-size: 18px;
  font-weight: 600;
  margin: 24px 0 12px 0;
}
</style>
