<template>
  <div class="demo-container">
    <div class="demo-header">
      <h1>RichTextEditor 富文本编辑器</h1>
      <p>基于 wangEditor 封装的 Vue 3 富文本编辑器组件。</p>
    </div>

    <a-card title="基础用法" class="demo-section">
      <RichTextEditor
        v-model="content"
        placeholder="请输入文章内容..."
        height="300px"
      />
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import RichTextEditor from '@/components/RichTextEditor'

const content = ref('<p>这是一个富文本编辑器示例</p>')
</script>

<style scoped>
.demo-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 32px;
}

.demo-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-1);
}

.demo-header p {
  margin: 0;
  font-size: 16px;
  color: var(--color-text-2);
  line-height: 1.6;
}

.demo-section {
  margin-bottom: 24px;
}
</style>
