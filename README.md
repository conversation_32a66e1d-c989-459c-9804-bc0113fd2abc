# OfficialBlock

一个基于 Vue 3 + TypeScript 的组件库，提供高质量的 UI 组件。

## 📖 文档

本项目包含完整的在线文档系统，类似 Element Plus 官方文档的设计：

- **开发指南**：介绍、安装、快速开始
- **组件文档**：每个组件的详细使用说明和 API 文档
- **在线演示**：可交互的组件示例

### 本地查看文档

```bash
# 克隆项目
git clone <your-repo-url>
cd OfficialBlock

# 安装依赖
npm install

# 启动文档服务器
npm run dev

# 访问 http://localhost:5166
```

## Installation

```bash
npm install officialblock
```

## Usage

### 方式一：全部引入

```javascript
import { createApp } from 'vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)

// 现在可以在任何组件中使用
// <ArticleList model-value="test" />
// <HeroSlide />
```

### 方式二：按需引入组件

```javascript
// Vue 3 Composition API
<template>
  <div>
    <ArticleList model-value="Hello" />
    <HeroSlide />
  </div>
</template>

<script setup>
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'
</script>
```

```javascript
// Vue 3 Options API
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

export default {
  components: {
    ArticleList,
    HeroSlide
  },
  data() {
    return {
      articleValue: 'Hello World'
    }
  }
}
```

### 方式三：按需引入插件

```javascript
import { createApp } from 'vue'
import { ArticleListPlugin, HeroSlidePlugin } from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(ArticleListPlugin)  // 只注册 ArticleList 组件
app.use(HeroSlidePlugin)    // 只注册 HeroSlide 组件
```

### TypeScript Support

This package includes TypeScript definitions.

```typescript
import type {
  ComponentProps,
  ComponentEmits,
  ComponentSlots,
  HeroSlideProps,
  HeroSlideEmits,
  SlideItem
} from 'officialblock'

// 使用类型
const articleProps: ComponentProps = {
  modelValue: 'Hello',
  size: 'medium',
  disabled: false
}

const heroProps: HeroSlideProps = {
  autoPlayInterval: 5000,
  showIndicators: true,
  autoPlay: true
}
```

## Components

### ArticleList 文章列表组件

```vue
<template>
  <ArticleList
    v-model="articleValue"
    size="medium"
    :disabled="false"
    @change="handleChange"
  >
    <template #header="{ title }">
      <h3>{{ title }}</h3>
    </template>
    <template #default="{ value }">
      <p>当前值: {{ value }}</p>
    </template>
  </ArticleList>
</template>

<script setup>
import { ref } from 'vue'
import { ArticleList } from 'officialblock'

const articleValue = ref('Hello World')

const handleChange = (value) => {
  console.log('值改变了:', value)
}
</script>
```

**Props:**
- `modelValue`: 双向绑定的值
- `size`: 组件尺寸 (`'small' | 'medium' | 'large'`)
- `disabled`: 是否禁用

**Events:**
- `change`: 值改变时触发
- `update:modelValue`: v-model 更新事件

**Slots:**
- `default`: 默认内容插槽
- `header`: 头部内容插槽

### HeroSlide 轮播图组件

```vue
<template>
  <HeroSlide
    :auto-play-interval="5000"
    :show-indicators="true"
    :auto-play="true"
    @change="handleSlideChange"
  />
</template>

<script setup>
import { HeroSlide } from 'officialblock'

const handleSlideChange = (index) => {
  console.log('切换到第', index + 1, '张')
}
</script>
```

**Props:**
- `autoPlayInterval`: 自动播放间隔时间（毫秒），默认 3000
- `showIndicators`: 是否显示指示器，默认 true
- `autoPlay`: 是否启用自动播放，默认 true

**Events:**
- `change`: 幻灯片切换时触发

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## License

MIT
