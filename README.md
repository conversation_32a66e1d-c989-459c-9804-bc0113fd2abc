# OfficialBlock

A Vue 3 article list component library.

## Installation

```bash
npm install officialblock
```

## Usage

### Global Registration

```javascript
import { createApp } from 'vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)
```

### Local Registration

```javascript
import { ArticleList } from 'officialblock'
import 'officialblock/style.css'

export default {
  components: {
    ArticleList
  }
}
```

### TypeScript Support

This package includes TypeScript definitions.

```typescript
import type { ComponentProps } from 'officialblock'
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## License

MIT
