# OfficialBlock

A Vue 3 article list component library.

## Installation

```bash
npm install officialblock
```

## Usage

### 方式一：全部引入

```javascript
import { createApp } from 'vue'
import OfficialBlock from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(OfficialBlock)

// 现在可以在任何组件中使用
// <ArticleList model-value="test" />
// <HeroSlide />
```

### 方式二：按需引入组件

```javascript
// Vue 3 Composition API
<template>
  <div>
    <ArticleList model-value="Hello" />
    <HeroSlide />
  </div>
</template>

<script setup>
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'
</script>
```

```javascript
// Vue 3 Options API
import { ArticleList, HeroSlide } from 'officialblock'
import 'officialblock/style.css'

export default {
  components: {
    ArticleList,
    HeroSlide
  },
  data() {
    return {
      articleValue: 'Hello World'
    }
  }
}
```

### 方式三：按需引入插件

```javascript
import { createApp } from 'vue'
import { ArticleListPlugin, HeroSlidePlugin } from 'officialblock'
import 'officialblock/style.css'

const app = createApp(App)
app.use(ArticleListPlugin)  // 只注册 ArticleList 组件
app.use(HeroSlidePlugin)    // 只注册 HeroSlide 组件
```

### TypeScript Support

This package includes TypeScript definitions.

```typescript
import type { ComponentProps, ComponentEmits, ComponentSlots } from 'officialblock'
```

## Components

- **ArticleList**: 文章列表组件
- **HeroSlide**: 轮播图组件

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## License

MIT
