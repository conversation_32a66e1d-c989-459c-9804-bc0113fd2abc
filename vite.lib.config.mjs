import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import { resolve } from "path"
import { fileURLToPath, URL } from "node:url"
import dts from "vite-plugin-dts"

export default defineConfig({
  plugins: [
    vue(),
    dts({
      entryRoot: "src",
      outDir: "dist/types",
      include: ["src/**/*.ts", "src/**/*.vue"],
      exclude: ["src/**/*.test.*", "src/**/*.spec.*", "src/main.ts", "src/App.vue"],
      staticImport: true,
      insertTypesEntry: true,
      copyDtsFiles: true
    })
  ],
  build: {
    lib: {
      entry: resolve(fileURLToPath(new URL(".", import.meta.url)), "src/index.ts"),
      name: "OfficialBlock",
      formats: ["es", "umd", "cjs"],
      fileName: (format) => `official-block.${format}.js`
    },
    rollupOptions: {
      external: ["vue", "vuedraggable"],
      output: {
        globals: {
          vue: "Vue",
          vuedraggable: "vuedraggable"
        },
        exports: "named"
      }
    }
  },
  resolve: {
    alias: {
      "@": resolve(fileURLToPath(new URL(".", import.meta.url)), "src")
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler', // 使用现代 Sass API
        additionalData: (content, filename) => {
          if (filename.includes("variables.scss")) {
            return content
          }
          return `@use "@/styles/variables.scss" as *;\n${content}`
        },
        includePaths: [resolve(fileURLToPath(new URL(".", import.meta.url)), "src")]
      }
    }
  }
})
